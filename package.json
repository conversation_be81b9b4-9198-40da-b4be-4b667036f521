{"name": "qihaozhushou-app", "private": true, "version": "1.3.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "generate-api-types": "npx openapi-typescript http://localhost:8200/common/docs/openapi/getOpenAPISpec -o src/api/generated/types.ts"}, "devDependencies": {"@tailwindcss/vite": "^4.1.4", "@types/node": "^22.15.19", "@types/qs": "^6.9.18", "@types/react": "^18.2.56", "@types/react-dom": "^18.2.19", "@types/react-helmet": "^6.1.11", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "daisyui": "^5.0.22", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "openapi-typescript": "^7.8.0", "tailwindcss": "^4.1.4", "typescript": "^5.2.2", "vite": "^5.1.4", "vite-plugin-svgr": "^4.3.0"}, "dependencies": {"@formily/core": "^2.3.3", "@formily/react": "^2.3.3", "@formily/reactive": "^2.3.3", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-table": "^8.21.3", "axios": "^1.8.4", "clsx": "^2.1.1", "dayjs": "^1.11.13", "deepmerge": "^4.3.1", "eventsource": "^3.0.6", "immer": "^10.1.1", "openapi-fetch": "^0.14.0", "qs": "^6.14.0", "rc-dialog": "^10.0.0", "react": "^18.2.0", "react-daisyui": "^5.0.5", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-markdown": "^10.1.0", "react-router-dom": "^7.5.0", "react-toastify": "^11.0.5", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.2.0", "zustand": "^5.0.3"}, "packageManager": "pnpm@9.11.0", "pnpm": {"overrides": {"daisyui": "^5.0.22"}, "patchedDependencies": {"react-daisyui": "patches/react-daisyui.patch"}}}
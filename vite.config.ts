import path from "path";
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import tailwindcss from '@tailwindcss/vite';
import svgr from "vite-plugin-svgr";


export default defineConfig({
  server: {
    host: "0.0.0.0",
  },
  base: process.env.PUBLIC_PATH,
  plugins: [tailwindcss(), react(), svgr()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
});
diff --git a/dist/Modal/Modal.d.ts b/dist/Modal/Modal.d.ts
index 392b03130936944b7ffa80965b106646dd4d3384..d8a0f1053ad9524460604fbd7ae5e0dbae011091 100644
--- a/dist/Modal/Modal.d.ts
+++ b/dist/Modal/Modal.d.ts
@@ -5,6 +5,7 @@ export type ModalProps = React.DialogHTMLAttributes<HTMLDialogElement> & ICompon
     responsive?: boolean;
     backdrop?: boolean;
     ariaHidden?: boolean;
+    containerClasses?: string;
 };
 export type DialogProps = Omit<ModalProps, 'ref'>;
 declare const _default: React.ForwardRefExoticComponent<React.DialogHTMLAttributes<HTMLDialogElement> & IComponentBaseProps & {
@@ -12,6 +13,7 @@ declare const _default: React.ForwardRefExoticComponent<React.DialogHTMLAttribut
     responsive?: boolean | undefined;
     backdrop?: boolean | undefined;
     ariaHidden?: boolean | undefined;
+    containerClasses?: string;
 } & React.RefAttributes<HTMLDialogElement>> & {
     Header: React.ForwardRefExoticComponent<React.HTMLAttributes<HTMLDivElement> & React.RefAttributes<HTMLDivElement>>;
     Body: React.ForwardRefExoticComponent<React.HTMLAttributes<HTMLDivElement> & React.RefAttributes<HTMLDivElement>>;
diff --git a/dist/react-daisyui.cjs b/dist/react-daisyui.cjs
index c01cbcefd83874ef4f36685e2cc5720e93b8a9ac..da6f188753238ffa69301b14f4a57a7dc180d0f1 100644
--- a/dist/react-daisyui.cjs
+++ b/dist/react-daisyui.cjs
@@ -2856,7 +2856,7 @@ var ModalHeader = /*#__PURE__*/React__default["default"].forwardRef(function (_r
 });
 ModalHeader.displayName = 'ModalHeader';
 
-var _excluded$1q = ["children", "open", "responsive", "onClickBackdrop", "dataTheme", "className"];
+var _excluded$1q = ["children", "open", "responsive", "onClickBackdrop", "dataTheme", "className", "containerClasses"];
 var Modal$2 = /*#__PURE__*/React.forwardRef(function (_ref, ref) {
   var children = _ref.children,
     open = _ref.open,
@@ -2864,18 +2864,19 @@ var Modal$2 = /*#__PURE__*/React.forwardRef(function (_ref, ref) {
     onClickBackdrop = _ref.onClickBackdrop,
     dataTheme = _ref.dataTheme,
     className = _ref.className,
+    containerClasses = _ref.containerClasses,
     props = _objectWithoutPropertiesLoose(_ref, _excluded$1q);
-  var containerClasses = twMerge('modal', clsx({
+  var _containerClasses = twMerge('modal', clsx({
     'modal-open': open,
     'modal-bottom sm:modal-middle': responsive
-  }));
+  }), containerClasses);
   var bodyClasses = twMerge('modal-box', className);
   return jsxRuntime.jsx("div", {
     "aria-label": "Modal",
     "aria-hidden": !open,
     "aria-modal": open,
     "data-theme": dataTheme,
-    className: containerClasses,
+    className: _containerClasses,
     onClick: function onClick(e) {
       e.stopPropagation();
       if (e.target === e.currentTarget) {
@@ -2895,7 +2896,7 @@ var Modal$2 = /*#__PURE__*/React.forwardRef(function (_ref, ref) {
 });
 Modal$2.displayName = 'Modal';
 
-var _excluded$1p = ["children", "open", "responsive", "backdrop", "ariaHidden", "dataTheme", "className"],
+var _excluded$1p = ["children", "open", "responsive", "backdrop", "ariaHidden", "dataTheme", "className", "containerClasses"],
   _excluded2 = ["children"];
 var Modal = /*#__PURE__*/React.forwardRef(function (_ref, ref) {
   var _ariaHidden;
@@ -2906,11 +2907,12 @@ var Modal = /*#__PURE__*/React.forwardRef(function (_ref, ref) {
     ariaHidden = _ref.ariaHidden,
     dataTheme = _ref.dataTheme,
     className = _ref.className,
+    containerClasses = _ref.containerClasses,
     props = _objectWithoutPropertiesLoose(_ref, _excluded$1p);
-  var containerClasses = twMerge('modal', clsx({
+  var _containerClasses = twMerge('modal', clsx({
     'modal-open': open,
     'modal-bottom sm:modal-middle': responsive
-  }));
+  }), containerClasses);
   ariaHidden = (_ariaHidden = ariaHidden) != null ? _ariaHidden : !open;
   var bodyClasses = twMerge('modal-box', className);
   return jsxRuntime.jsxs("dialog", _extends({}, props, {
@@ -2919,7 +2921,7 @@ var Modal = /*#__PURE__*/React.forwardRef(function (_ref, ref) {
     open: open,
     "aria-modal": open,
     "data-theme": dataTheme,
-    className: containerClasses,
+    className: _containerClasses,
     ref: ref,
     children: [jsxRuntime.jsx("div", {
       "data-theme": dataTheme,
diff --git a/dist/react-daisyui.esm.js b/dist/react-daisyui.esm.js
index 577f5b802cefb862532e03b6e209f45703ce1a00..e9e8de3a04b5f36af34c5a73f28ffe4994edb594 100644
--- a/dist/react-daisyui.esm.js
+++ b/dist/react-daisyui.esm.js
@@ -2852,7 +2852,7 @@ var ModalHeader = /*#__PURE__*/React.forwardRef(function (_ref, ref) {
 });
 ModalHeader.displayName = 'ModalHeader';
 
-var _excluded$1q = ["children", "open", "responsive", "onClickBackdrop", "dataTheme", "className"];
+var _excluded$1q = ["children", "open", "responsive", "onClickBackdrop", "dataTheme", "className", "containerClasses"];
 var Modal$2 = /*#__PURE__*/forwardRef(function (_ref, ref) {
   var children = _ref.children,
     open = _ref.open,
@@ -2860,18 +2860,19 @@ var Modal$2 = /*#__PURE__*/forwardRef(function (_ref, ref) {
     onClickBackdrop = _ref.onClickBackdrop,
     dataTheme = _ref.dataTheme,
     className = _ref.className,
+    containerClasses = _ref.containerClasses,
     props = _objectWithoutPropertiesLoose(_ref, _excluded$1q);
-  var containerClasses = twMerge('modal', clsx({
+  var _containerClasses = twMerge('modal', clsx({
     'modal-open': open,
     'modal-bottom sm:modal-middle': responsive
-  }));
+  }), containerClasses);
   var bodyClasses = twMerge('modal-box', className);
   return jsx("div", {
     "aria-label": "Modal",
     "aria-hidden": !open,
     "aria-modal": open,
     "data-theme": dataTheme,
-    className: containerClasses,
+    className: _containerClasses,
     onClick: function onClick(e) {
       e.stopPropagation();
       if (e.target === e.currentTarget) {
@@ -2891,7 +2892,7 @@ var Modal$2 = /*#__PURE__*/forwardRef(function (_ref, ref) {
 });
 Modal$2.displayName = 'Modal';
 
-var _excluded$1p = ["children", "open", "responsive", "backdrop", "ariaHidden", "dataTheme", "className"],
+var _excluded$1p = ["children", "open", "responsive", "backdrop", "ariaHidden", "dataTheme", "className", "containerClasses"],
   _excluded2 = ["children"];
 var Modal = /*#__PURE__*/forwardRef(function (_ref, ref) {
   var _ariaHidden;
@@ -2902,11 +2903,12 @@ var Modal = /*#__PURE__*/forwardRef(function (_ref, ref) {
     ariaHidden = _ref.ariaHidden,
     dataTheme = _ref.dataTheme,
     className = _ref.className,
+    containerClasses = _ref.containerClasses,
     props = _objectWithoutPropertiesLoose(_ref, _excluded$1p);
-  var containerClasses = twMerge('modal', clsx({
+  var _containerClasses = twMerge('modal', clsx({
     'modal-open': open,
     'modal-bottom sm:modal-middle': responsive
-  }));
+  }), containerClasses);
   ariaHidden = (_ariaHidden = ariaHidden) != null ? _ariaHidden : !open;
   var bodyClasses = twMerge('modal-box', className);
   return jsxs("dialog", _extends({}, props, {
@@ -2915,7 +2917,7 @@ var Modal = /*#__PURE__*/forwardRef(function (_ref, ref) {
     open: open,
     "aria-modal": open,
     "data-theme": dataTheme,
-    className: containerClasses,
+    className: _containerClasses,
     ref: ref,
     children: [jsx("div", {
       "data-theme": dataTheme,
diff --git a/dist/react-daisyui.modern.js b/dist/react-daisyui.modern.js
index 9a0ee6bc8f44c05e026577e165c207cb6554b3ff..6f74c7796d2787506e35b620d75ff7d738fec1ba 100644
--- a/dist/react-daisyui.modern.js
+++ b/dist/react-daisyui.modern.js
@@ -2866,7 +2866,7 @@ const ModalHeader = /*#__PURE__*/React.forwardRef((_ref, ref) => {
 });
 ModalHeader.displayName = 'ModalHeader';
 
-const _excluded$1q = ["children", "open", "responsive", "onClickBackdrop", "dataTheme", "className"];
+const _excluded$1q = ["children", "open", "responsive", "onClickBackdrop", "dataTheme", "className", "containerClasses"];
 const Modal$2 = /*#__PURE__*/forwardRef((_ref, ref) => {
   let {
       children,
@@ -2874,13 +2874,14 @@ const Modal$2 = /*#__PURE__*/forwardRef((_ref, ref) => {
       responsive,
       onClickBackdrop,
       dataTheme,
-      className
+      className,
+      containerClasses
     } = _ref,
     props = _objectWithoutPropertiesLoose(_ref, _excluded$1q);
-  const containerClasses = twMerge('modal', clsx({
+  const _containerClasses = twMerge('modal', clsx({
     'modal-open': open,
     'modal-bottom sm:modal-middle': responsive
-  }));
+  }), containerClasses);
   const bodyClasses = twMerge('modal-box', className);
   return jsx("div", {
     "aria-label": "Modal",
@@ -2907,7 +2908,7 @@ const Modal$2 = /*#__PURE__*/forwardRef((_ref, ref) => {
 });
 Modal$2.displayName = 'Modal';
 
-const _excluded$1p = ["children", "open", "responsive", "backdrop", "ariaHidden", "dataTheme", "className"],
+const _excluded$1p = ["children", "open", "responsive", "backdrop", "ariaHidden", "dataTheme", "className", "containerClasses"],
   _excluded2 = ["children"];
 const Modal = /*#__PURE__*/forwardRef((_ref, ref) => {
   var _ariaHidden;
@@ -2918,13 +2919,14 @@ const Modal = /*#__PURE__*/forwardRef((_ref, ref) => {
       backdrop,
       ariaHidden,
       dataTheme,
-      className
+      className,
+      containerClasses
     } = _ref,
     props = _objectWithoutPropertiesLoose(_ref, _excluded$1p);
-  const containerClasses = twMerge('modal', clsx({
+  const _containerClasses = twMerge('modal', clsx({
     'modal-open': open,
     'modal-bottom sm:modal-middle': responsive
-  }));
+  }), containerClasses);
   ariaHidden = (_ariaHidden = ariaHidden) != null ? _ariaHidden : !open;
   const bodyClasses = twMerge('modal-box', className);
   return jsxs("dialog", _extends({}, props, {
@@ -2933,7 +2935,7 @@ const Modal = /*#__PURE__*/forwardRef((_ref, ref) => {
     open: open,
     "aria-modal": open,
     "data-theme": dataTheme,
-    className: containerClasses,
+    className: _containerClasses,
     ref: ref,
     children: [jsx("div", {
       "data-theme": dataTheme,

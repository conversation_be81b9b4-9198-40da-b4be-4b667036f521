name: prod deploy

env:
  SERVICE_NAME: "[正式环境] 起号助手APP"
  LOCAL_PATH: dist
  OSS_PATH: app
  ROUTER_BASENAME: /
  SERVICE_URL: https://qihaozhushou.com/app
  CDN_URL: https://cdn.qihaozhushou.com
  ENVIRONMENT_TEXT: prod
  OSS_REGION: oss-cn-shanghai
  OSS_BUCKET: qihaozhushou

on:
  #  workflow_run 必须在默认分支或主分支
  workflow_run:
    workflows:
      - build-and-dev-deploy
    types:
      - completed
    branches:
      - main


jobs:
  version:
    if: ${{ github.event.workflow_run.conclusion == 'success' }}
    uses: 0xTeams/reuse-workflows/.github/workflows/version.download.yml@main

  vars:
    needs: [version]
    runs-on: ubuntu-latest
    steps: 
      - run: echo vars
    outputs:
      version: ${{ needs.version.outputs.version }}
      service-name: ${{ env.SERVICE_NAME }}
      public-path: ${{ env.CDN_URL }}/${{ env.OSS_PATH }}/
      local-path: ${{ env.LOCAL_PATH }}
      oss-path: ${{ env.OSS_PATH }}
      oss-region: ${{ env.OSS_REGION }}
      oss-bucket: ${{ env.OSS_BUCKET }}
      cdn-index-path-prefix: ${{ env.CDN_URL }}/${{ env.OSS_PATH }}
      service-url: ${{ env.SERVICE_URL }}
      environment: ${{ env.ENVIRONMENT_TEXT }}


  approval:
    needs: [vars]
    runs-on: ubuntu-22.04
    steps:
      - uses: trstringer/manual-approval@v1
        timeout-minutes: 1
        with:
          issue-title: ${{ needs.vars.outputs.version }}
          secret: ${{ secrets.PAT_TOKEN }}
          approvers: frontend
          minimum-approvals: 1


  upload:
    needs: [vars, approval]
    uses: 0xTeams/reuse-workflows/.github/workflows/web.upload-ali-oss-with-refresh_new.yml@main
    secrets:
      oss-key-id: ${{ secrets.OSS_KEY_ID }}
      oss-key-secret: ${{ secrets.OSS_KEY_SECRET }}
    with:
      version: ${{ needs.vars.outputs.version }}
      oss-path: ${{ needs.vars.outputs.oss-path }}
      oss-region: ${{ needs.vars.outputs.oss-region }}
      local-path: ${{ needs.vars.outputs.local-path }}
      oss-bucket: ${{ needs.vars.outputs.oss-bucket }}
      refresh-path-prefix: ${{ needs.vars.outputs.cdn-index-path-prefix }}
      namespace: ${{ needs.vars.outputs.environment }}


  feishu-success-notice:
    needs: [vars, upload]
    uses: 0xTeams/reuse-workflows/.github/workflows/feishu.success.notice.yml@main
    secrets: inherit
    with:
      version: ${{ needs.vars.outputs.version }}
      service-name: ${{ needs.vars.outputs.service-name }}
      service-url: ${{ needs.vars.outputs.service-url }}
      environment-name: ${{ needs.vars.outputs.environment }}



  feishu-failure-notice:
    if: failure()
    needs: [vars, upload]
    uses: 0xTeams/reuse-workflows/.github/workflows/feishu.failure.notice.yml@main
    secrets: inherit
    with:
      service-name: ${{ needs.vars.outputs.service-name }}
      version: ${{ needs.vars.outputs.version }}
      environment-name: ${{ needs.vars.outputs.environment }}
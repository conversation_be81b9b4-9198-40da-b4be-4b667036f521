name: build-and-dev-deploy

env:
  SERVICE_NAME: "[测试环境] 起号助手APP"
  LOCAL_PATH: dist
  OSS_PATH: app
  ROUTER_BASENAME: /
  SERVICE_URL: https://test.qihaozhushou.com/app
  CDN_URL: https://cdn.qihaozhushou.com
  ENVIRONMENT_TEXT: test
  OSS_REGION: oss-cn-shanghai
  OSS_BUCKET: qihaozhushou

on:
  push:
    branches:
      - main
      - dev
      - version/**
      - feature/**

jobs:
  version:
    uses: 0xTeams/neobio-reuse-workflows/.github/workflows/version.upload.yml@main

  vars:
    needs: [version]
    runs-on: ubuntu-latest
    steps:
      - run: echo vars
    outputs:
      version: ${{ needs.version.outputs.version }}
      service-name: ${{ env.SERVICE_NAME }}
      docker-image-name: ${{ env.DOCKER_IMAGE_NAME }}
      public-path: ${{ env.CDN_URL }}/${{ env.OSS_PATH }}/
      local-path: ${{ env.LOCAL_PATH }}
      oss-path: ${{ env.OSS_PATH }}
      oss-region: ${{ env.OSS_REGION }}
      oss-bucket: ${{ env.OSS_BUCKET }}
      cdn-index-path-prefix: ${{ env.CDN_URL }}/${{ env.OSS_PATH }}
      service-url: ${{ env.SERVICE_URL }}
      environment: ${{ env.ENVIRONMENT_TEXT }}
      router-basename: ${{ env.ROUTER_BASENAME }}


  build:
    needs: [vars]
    uses: 0xTeams/reuse-workflows/.github/workflows/web.build.yml@main
    secrets: inherit
    with:
      build-run: npm run build
      version: ${{ needs.vars.outputs.version }}
      local-path: ${{ needs.vars.outputs.local-path }}
      public-path: ${{ needs.vars.outputs.public-path }}
      node-version: '22'
      router-basename: ${{ needs.vars.outputs.router-basename }}


  upload:
    needs: [vars, build]
    uses: 0xTeams/reuse-workflows/.github/workflows/web.upload-ali-oss-with-refresh_new.yml@main
    secrets:
      oss-key-id: ${{ secrets.OSS_KEY_ID }}
      oss-key-secret: ${{ secrets.OSS_KEY_SECRET }}
    with:
      version: ${{ needs.vars.outputs.version }}
      oss-path: ${{ needs.vars.outputs.oss-path }}
      local-path: ${{ needs.vars.outputs.local-path }}
      oss-region: ${{ needs.vars.outputs.oss-region }}
      oss-bucket: ${{ needs.vars.outputs.oss-bucket }}
      refresh-path-prefix: ${{ needs.vars.outputs.cdn-index-path-prefix }}
      namespace: ${{  needs.vars.outputs.environment }}


  feishu-success-notice:
    needs: [vars, upload]
    uses: 0xTeams/reuse-workflows/.github/workflows/feishu.success.notice.yml@main
    secrets: inherit
    with:
      version: ${{ needs.vars.outputs.version }}
      service-name: ${{ needs.vars.outputs.service-name }}
      service-url: ${{ needs.vars.outputs.service-url }}
      environment-name: ${{ needs.vars.outputs.environment }}


  feishu-failure-notice:
    if: failure()
    needs: [vars, build, upload]
    uses: 0xTeams/reuse-workflows/.github/workflows/feishu.failure.notice.yml@main
    secrets: inherit
    with:
      service-name: ${{ needs.vars.outputs.service-name }}
      version: ${{ needs.vars.outputs.version }}
      environment-name: ${{ needs.vars.outputs.environment }}
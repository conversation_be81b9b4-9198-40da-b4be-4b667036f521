// 测试 Inbox 页面 API 集成功能
import { apiClient } from '@/api/generated/client';

export async function testInboxApiIntegration() {
  console.log('🔍 测试 Inbox 页面 API 集成功能...');
  
  const tests = [
    {
      name: '测试获取相关视频列表',
      test: async () => {
        try {
          const { data, error } = await apiClient.GET('/client/user/douyin/getVideoRelatedList', {
            params: {
              query: {
                page: 1,
                page_size: 10,
                sort_by: 'create_time',
                sort_order: 'desc'
              }
            }
          });
          
          if (error) {
            console.log('❌ 请求失败:', error);
            return { success: false, message: `API 错误: ${error.msg}` };
          }
          
          if (data?.code === 0) {
            console.log('✅ 获取相关视频列表成功:', {
              total: data.data?.total,
              count: data.data?.list?.length,
              page: data.data?.page
            });
            return {
              success: true,
              message: `成功获取 ${data.data?.list?.length || 0} 条记录`,
              data: data.data
            };
          } else {
            return { success: false, message: data?.message || '未知错误' };
          }
        } catch (error: any) {
          console.error('❌ 网络错误:', error);
          return { success: false, message: error.message };
        }
      }
    },
    
    {
      name: '测试按来源类型筛选',
      test: async () => {
        try {
          const sourceTypes = ['collect', 'author', 'video'] as const;
          const results = [];
          
          for (const sourceType of sourceTypes) {
            const { data, error } = await apiClient.GET('/client/user/douyin/getVideoRelatedList', {
              params: {
                query: {
                  source_type: sourceType,
                  page: 1,
                  page_size: 5
                }
              }
            });
            
            if (error) {
              results.push({ sourceType, success: false, error: error.msg });
            } else if (data?.code === 0) {
              results.push({ 
                sourceType, 
                success: true, 
                count: data.data?.list?.length || 0 
              });
            }
          }
          
          console.log('✅ 来源类型筛选测试结果:', results);
          return { 
            success: true, 
            message: `测试了 ${sourceTypes.length} 种来源类型`,
            data: results
          };
        } catch (error: any) {
          return { success: false, message: error.message };
        }
      }
    },
    
    {
      name: '测试获取视频详情',
      test: async () => {
        try {
          // 先获取一个视频ID
          const { data: listData } = await apiClient.GET('/client/user/douyin/getVideoRelatedList', {
            params: {
              query: { page: 1, page_size: 1 }
            }
          });
          
          if (!listData?.data?.list?.[0]?.aweme_id) {
            return { success: false, message: '没有找到可测试的视频ID' };
          }
          
          const awemeId = listData.data.list[0].aweme_id;
          
          const { data, error } = await apiClient.GET('/client/user/douyin/getVideoDetail', {
            params: {
              query: { aweme_id: awemeId }
            }
          });
          
          if (error) {
            console.log('❌ 获取视频详情失败:', error);
            return { success: false, message: `API 错误: ${error.msg}` };
          }
          
          if (data?.code === 0) {
            console.log('✅ 获取视频详情成功:', {
              aweme_id: data.data?.aweme_id,
              title: data.data?.title,
              author: data.data?.nickname,
              likes: data.data?.liked_count
            });
            return { 
              success: true, 
              message: '成功获取视频详情',
              data: data.data
            };
          } else {
            return { success: false, message: data?.msg || '未知错误' };
          }
        } catch (error: any) {
          console.error('❌ 获取视频详情失败:', error);
          return { success: false, message: error.message };
        }
      }
    },
    
    {
      name: '测试分页功能',
      test: async () => {
        try {
          const pages = [1, 2];
          const results = [];
          
          for (const page of pages) {
            const { data, error } = await apiClient.GET('/client/user/douyin/getVideoRelatedList', {
              params: {
                query: {
                  page,
                  page_size: 5
                }
              }
            });
            
            if (error) {
              results.push({ page, success: false, error: error.msg });
            } else if (data?.code === 0) {
              results.push({ 
                page, 
                success: true, 
                count: data.data?.list?.length || 0,
                total: data.data?.total
              });
            }
          }
          
          console.log('✅ 分页功能测试结果:', results);
          return { 
            success: true, 
            message: `测试了 ${pages.length} 页数据`,
            data: results
          };
        } catch (error: any) {
          return { success: false, message: error.message };
        }
      }
    }
  ];
  
  console.log('🚀 开始执行 Inbox API 集成测试...');
  
  const results = [];
  for (const test of tests) {
    console.log(`\n📋 执行测试: ${test.name}`);
    try {
      const result = await test.test();
      results.push({ name: test.name, ...result });
      console.log(`${result.success ? '✅' : '❌'} ${test.name}: ${result.message}`);
    } catch (error: any) {
      results.push({ name: test.name, success: false, message: error.message });
      console.log(`❌ ${test.name}: ${error.message}`);
    }
  }
  
  console.log('\n📊 测试结果汇总:');
  results.forEach(result => {
    console.log(`${result.success ? '✅' : '❌'} ${result.name}: ${result.message}`);
  });
  
  const successCount = results.filter(r => r.success).length;
  console.log(`\n🎯 测试完成: ${successCount}/${results.length} 个测试通过`);
  
  return {
    success: successCount === results.length,
    results,
    summary: `${successCount}/${results.length} 个测试通过`
  };
}

// 在浏览器控制台中可以调用这个函数进行测试
if (typeof window !== 'undefined') {
  (window as any).testInboxApiIntegration = testInboxApiIntegration;
  console.log('💡 在控制台中运行 testInboxApiIntegration() 来测试 Inbox API 集成功能');
}

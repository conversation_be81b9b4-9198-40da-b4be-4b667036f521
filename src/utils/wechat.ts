import qs from 'qs';

export function isWeChatBrowser() {
    // 获取浏览器的 userAgent 并转换为小写
    const userAgent = window.navigator.userAgent.toLowerCase();

    // 检测 userAgent 中是否包含 'micromessenger' 字符串
    if (userAgent.match(/micromessenger/i)) {
        return true;
    } else {
        return false;
    }
}

const WECHAT_OFFIACCOUNT_AUTHORIZE_URL = 'https://open.weixin.qq.com/connect/oauth2/authorize';

export function wechatOffiaccountLogin(params: { appid: string, redirect_uri: string }) {
    const search = qs.stringify({
        response_type: 'code',
        scope: 'snsapi_userinfo',
        ...params,
    })
    const fullUrl = `${WECHAT_OFFIACCOUNT_AUTHORIZE_URL}?${search}#wechat_redirect`
    window.location.href = fullUrl;
}
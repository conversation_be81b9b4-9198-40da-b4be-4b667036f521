import { toast, ToastOptions } from 'react-toastify';
import { ReactNode } from 'react';

// DaisyUI 主题集成的 Toast 配置
const defaultOptions: ToastOptions = {
    position: 'top-right',
    autoClose: 4000,
    hideProgressBar: true, // 默认隐藏进度条
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    progress: undefined,
    theme: 'colored',
    style: {
        fontFamily: 'inherit',
    },
};

// 全局进度条配置
let globalProgressBarSetting = true; // 是否隐藏进度条

// Toast 类型定义
type ToastType = 'success' | 'error' | 'warning' | 'info' | 'default';

// 扩展的 Toast 选项
interface ExtendedToastOptions extends ToastOptions {
    duration?: number;
    persistent?: boolean;
    withSound?: boolean;
}

// Toast 配置管理
export const toastConfig = {
    /**
     * 设置是否隐藏进度条（全局配置）
     * @param hide - true: 隐藏进度条, false: 显示进度条
     */
    setHideProgressBar: (hide: boolean) => {
        globalProgressBarSetting = hide;
        defaultOptions.hideProgressBar = hide;
    },

    /**
     * 获取当前进度条隐藏设置
     */
    getHideProgressBar: () => globalProgressBarSetting,

    /**
     * 重置为默认配置
     */
    reset: () => {
        globalProgressBarSetting = true;
        defaultOptions.hideProgressBar = true;
    },
};

export const showToast = {
    /**
     * 显示成功提示
     */
    success: (message: ReactNode, options?: ExtendedToastOptions) => {
        const finalOptions = {
            ...defaultOptions,
            hideProgressBar: options?.hideProgressBar ?? globalProgressBarSetting,
            autoClose: options?.persistent ? false : (options?.duration || defaultOptions.autoClose) as number | false,
            ...options,
            className: 'toast-success-daisyui',
        };

        if (options?.withSound) {
            playNotificationSound('success');
        }

        return toast.success(message, finalOptions);
    },

    /**
     * 显示错误提示
     */
    error: (message: ReactNode, options?: ExtendedToastOptions) => {
        const finalOptions = {
            ...defaultOptions,
            hideProgressBar: options?.hideProgressBar ?? globalProgressBarSetting,
            autoClose: options?.persistent ? false : (options?.duration || 6000) as number | false, // 错误信息显示更久
            ...options,
            className: 'toast-error-daisyui',
        };

        if (options?.withSound) {
            playNotificationSound('error');
        }

        return toast.error(message, finalOptions);
    },

    /**
     * 显示警告提示
     */
    warning: (message: ReactNode, options?: ExtendedToastOptions) => {
        const finalOptions = {
            ...defaultOptions,
            hideProgressBar: options?.hideProgressBar ?? globalProgressBarSetting,
            autoClose: options?.persistent ? false : (options?.duration || 5000) as number | false,
            ...options,
            className: 'toast-warning-daisyui',
        };

        if (options?.withSound) {
            playNotificationSound('warning');
        }

        return toast.warning(message, finalOptions);
    },

    /**
     * 显示信息提示
     */
    info: (message: ReactNode, options?: ExtendedToastOptions) => {
        const finalOptions = {
            ...defaultOptions,
            hideProgressBar: options?.hideProgressBar ?? globalProgressBarSetting,
            autoClose: options?.persistent ? false : (options?.duration || defaultOptions.autoClose),
            ...options,
            className: 'toast-info-daisyui',
        };

        if (options?.withSound) {
            playNotificationSound('info');
        }

        return toast.info(message, finalOptions);
    },

    /**
     * 显示默认提示
     */
    default: (message: ReactNode, options?: ExtendedToastOptions) => {
        const finalOptions = {
            ...defaultOptions,
            hideProgressBar: options?.hideProgressBar ?? globalProgressBarSetting,
            autoClose: options?.persistent ? false : (options?.duration || defaultOptions.autoClose),
            ...options,
            className: 'toast-default-daisyui',
        };

        return toast(message, finalOptions);
    },

    /**
     * 显示自定义内容的提示
     */
    custom: (content: ReactNode, type: ToastType = 'default', options?: ExtendedToastOptions) => {
        const typeMethod = showToast[type];
        return typeMethod(content, options);
    },

    /**
     * Promise 状态提示
     */
    promise: <T>(
        promise: Promise<T>,
        messages: {
            pending: string;
            success: string;
            error: string;
        },
        options?: ExtendedToastOptions
    ) => {
        const finalOptions = {
            ...defaultOptions,
            ...options,
        };

        return toast.promise(promise, messages, finalOptions);
    },

    /**
     * 加载状态提示
     */
    loading: (message: ReactNode = '加载中...', options?: ExtendedToastOptions) => {
        return showToast.info(
            `🔄 ${message}`,
            {
                autoClose: false,
                closeOnClick: false,
                draggable: false,
                ...options,
            }
        );
    },

    /**
     * 更新已存在的 Toast
     */
    update: (toastId: string | number, options: ToastOptions & { render?: ReactNode; type?: ToastType }) => {
        return toast.update(toastId, options);
    },

    /**
     * 关闭指定的 Toast
     */
    dismiss: (toastId?: string | number) => {
        return toast.dismiss(toastId);
    },

    /**
     * 检查 Toast 是否激活
     */
    isActive: (toastId: string | number) => {
        return toast.isActive(toastId);
    },
};

// 声音通知功能（可选）
const playNotificationSound = (type: ToastType) => {
    if (typeof window === 'undefined' || !window.AudioContext) return;

    try {
        // 创建简单的提示音
        const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        // 根据类型设置不同的音调
        const frequencies = {
            success: 800,
            error: 300,
            warning: 600,
            info: 500,
            default: 400,
        };

        oscillator.frequency.setValueAtTime(frequencies[type], audioContext.currentTime);
        oscillator.type = 'sine';

        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.1);
    } catch (error) {
        // 忽略音频播放错误
        console.debug('Toast sound notification failed:', error);
    }
};

// 导出单独的方法以向后兼容
export const { success, error, warning, info } = showToast;

export default showToast;

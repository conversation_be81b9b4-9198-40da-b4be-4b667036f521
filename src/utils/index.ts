import dayjs from 'dayjs'

export function showTime(time: string | number | Date): string {
    return dayjs(time).format('YYYY-MM-DD')
}

export function formatDateTime(time: string | number | Date): string {
    return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 格式化互动数据显示，过万时保留一位小数
 * @param count 数字值
 * @returns 格式化后的字符串
 */
export function formatInteractionCount(count: number | string | undefined): string {
    if (!count || count === 0) return '0'

    const num = typeof count === 'string' ? parseInt(count, 10) : count

    if (isNaN(num)) return '0'

    if (num >= 10000) {
        return (num / 10000).toFixed(1) + '万'
    }

    return num.toString()
}
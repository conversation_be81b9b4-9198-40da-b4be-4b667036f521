import dayjs from 'dayjs'

export function showTime(time: string | number | Date): string {
    return dayjs(time).format('YYYY-MM-DD')
}

export function formatDateTime(time: string | number | Date): string {
    return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 格式化互动数据显示，过万时保留一位小数
 * @param count 数字值
 * @returns 格式化后的字符串
 */
export function formatInteractionCount(count: number | string | undefined): string {
    if (!count || count === 0) return '0'

    const num = typeof count === 'string' ? parseInt(count, 10) : count

    if (isNaN(num)) return '0'

    if (num >= 10000) {
        return (num / 10000).toFixed(1) + '万'
    }

    return num.toString()
}

/**
 * 安全处理 CreateTime 字段，支持新的可选字符串类型（date-time 格式）
 * @param createTime CreateTime 字段值，可能是字符串（ISO 8601 格式）或 undefined
 * @returns 格式化后的日期字符串，如果无效则返回空字符串
 */
export function formatCreateTime(createTime: string | undefined): string {
    if (!createTime) return ''

    try {
        // CreateTime 现在是 ISO 8601 格式的字符串，直接使用 dayjs 解析
        const date = dayjs(createTime)
        if (!date.isValid()) return ''

        return date.format('YYYY-MM-DD')
    } catch (error) {
        console.warn('CreateTime 格式化失败:', createTime, error)
        return ''
    }
}

/**
 * 安全处理 CreateTime 字段，返回完整的日期时间格式
 * @param createTime CreateTime 字段值，可能是字符串（ISO 8601 格式）或 undefined
 * @returns 格式化后的日期时间字符串，如果无效则返回空字符串
 */
export function formatCreateTimeDetailed(createTime: string | undefined): string {
    if (!createTime) return ''

    try {
        // CreateTime 现在是 ISO 8601 格式的字符串，直接使用 dayjs 解析
        const date = dayjs(createTime)
        if (!date.isValid()) return ''

        return date.format('YYYY-MM-DD HH:mm:ss')
    } catch (error) {
        console.warn('CreateTime 详细格式化失败:', createTime, error)
        return ''
    }
}

/**
 * 兼容旧版本的 CreateTime 处理函数，支持数字时间戳和新的字符串格式
 * @param createTime CreateTime 字段值，可能是数字时间戳、字符串或 undefined
 * @returns 格式化后的日期字符串，如果无效则返回空字符串
 */
export function formatCreateTimeCompat(createTime: string | number | undefined): string {
    if (!createTime) return ''

    try {
        let date: dayjs.Dayjs

        if (typeof createTime === 'number') {
            // 旧版本：数字时间戳（秒），需要转换为毫秒
            date = dayjs(createTime * 1000)
        } else {
            // 新版本：ISO 8601 格式的字符串
            date = dayjs(createTime)
        }

        if (!date.isValid()) return ''

        return date.format('YYYY-MM-DD')
    } catch (error) {
        console.warn('CreateTime 兼容格式化失败:', createTime, error)
        return ''
    }
}

/**
 * 兼容旧版本的 CreateTime 处理函数，返回完整的日期时间格式
 * @param createTime CreateTime 字段值，可能是数字时间戳、字符串或 undefined
 * @returns 格式化后的日期时间字符串，如果无效则返回空字符串
 */
export function formatCreateTimeCompatDetailed(createTime: string | number | undefined): string {
    if (!createTime) return ''

    try {
        let date: dayjs.Dayjs

        if (typeof createTime === 'number') {
            // 旧版本：数字时间戳（秒），需要转换为毫秒
            date = dayjs(createTime * 1000)
        } else {
            // 新版本：ISO 8601 格式的字符串
            date = dayjs(createTime)
        }

        if (!date.isValid()) return ''

        return date.format('YYYY-MM-DD')
    } catch (error) {
        console.warn('CreateTime 兼容详细格式化失败:', createTime, error)
        return ''
    }
}
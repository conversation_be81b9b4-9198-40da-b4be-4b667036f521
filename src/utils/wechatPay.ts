/**
 * WeChat Payment Utils
 * This file contains utility functions for WeChat payment integration
 */

import axios from 'axios';

// Define types for WeChat SDK configuration
interface WeChatConfig {
  debug: boolean;
  appId: string;
  timestamp: string;
  nonceStr: string;
  signature: string;
  jsApiList: string[];
}

// Define types for WeChat payment parameters
interface WeChatPayParams {
  timestamp: string;
  nonceStr: string;
  package: string;
  signType: string;
  paySign: string;
}

// Initialize WeChat SDK with the provided configuration
export const initWeChatSDK = (config: WeChatConfig): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (!window.wx) {
      reject(new Error('WeChat JSSDK not found. Make sure you are opening this page in WeChat browser.'));
      return;
    }

    window.wx.config({
      debug: config.debug,
      appId: config.appId,
      timestamp: config.timestamp,
      nonceStr: config.nonceStr,
      signature: config.signature,
      jsApiList: config.jsApiList,
    });

    window.wx.ready(() => {
      resolve();
    });

    window.wx.error((error: any) => {
      reject(error);
    });
  });
};

// Fetch payment configuration from server
export const fetchPaymentConfig = async (tradeNoForQuery: string): Promise<any> => {
  try {
    // Replace with your actual API endpoint
    const response = await axios.get(`/api/payment/wxconfig?trade_no_for_query=${tradeNoForQuery}`);
    return response.data;
  } catch (error) {
    console.error('Failed to fetch payment configuration:', error);
    throw error;
  }
};

// Initiate WeChat payment with the provided parameters
export const initiateWeChatPayment = (payParams: WeChatPayParams): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (!window.wx) {
      reject(new Error('WeChat JSSDK not found'));
      return;
    }

    window.wx.chooseWXPay({
      timestamp: payParams.timestamp,
      nonceStr: payParams.nonceStr,
      package: payParams.package,
      signType: payParams.signType,
      paySign: payParams.paySign,
      success: () => {
        resolve();
      },
      cancel: () => {
        reject(new Error('Payment canceled by user'));
      },
      fail: (res: any) => {
        reject(new Error(`Payment failed: ${JSON.stringify(res)}`));
      },
    });
  });
};

// Check if the current browser is WeChat browser
export const isWeChatBrowser = (): boolean => {
  const userAgent = navigator.userAgent.toLowerCase();
  return /micromessenger/i.test(userAgent);
};

// Check if the current browser is Alipay browser
export const isAlipayBrowser = (): boolean => {
  const userAgent = navigator.userAgent.toLowerCase();
  return /alipay/i.test(userAgent);
};

// Check if the current browser is a supported payment browser
export const isSupportedPaymentBrowser = (): boolean => {
  return isWeChatBrowser() || isAlipayBrowser();
}; 
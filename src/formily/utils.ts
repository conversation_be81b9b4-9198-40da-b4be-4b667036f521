import { Field } from "@formily/core"

export const takeMessage = (field: Field) => {
    const split = (messages: any[]) => {
        return messages.reduce((buf, text, index) => {
            if (!text) return buf
            return index < messages.length - 1
                ? buf.concat([text, ', '])
                : buf.concat([text])
        }, [])
    }
    if (field.validating) return
    // if (props.feedbackText) return props.feedbackText
    if (field.selfErrors.length) return split(field.selfErrors)
    if (field.selfWarnings.length) return split(field.selfWarnings)
    if (field.selfSuccesses.length) return split(field.selfSuccesses)
}
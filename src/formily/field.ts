import { FormItem } from "@/components/formily/FormItem"
import { Input } from "@/components/formily/Input"
import { Select } from "@/components/formily/Select"
import { Textarea } from "@/components/formily/Textarea"
import { createSchemaField } from "@formily/react"


export const SchemaField = createSchemaField({
    components: {
        FormItem,
        Input,
        Textarea,
        Select,
    },
    scope: {},
})
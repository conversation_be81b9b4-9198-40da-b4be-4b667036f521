import axios from 'axios'
import { useAuthStore } from '../models/auth'
import { router } from '../routes'
import { ROUTES } from '../routes/constants'
import { API_URL } from '@/config'
import { showToast } from '../utils/toast'
import { SELF_HANDLED_CODE } from './code'
import { CustomError } from '@/error'

const api = axios.create({
    baseURL: API_URL,
})

api.interceptors.request.use((requestConfig) => {
    const { state } = useAuthStore.getState()
    if (state.token) {
        requestConfig.headers.Authorization = state.token
    }
    return requestConfig
})

api.interceptors.response.use(async (response): Promise<any> => {
    const { data = {} } = response
    if (!data.code || data.code == 0) return response


    if (data.code == 401) {
        const queryParamsText = new URLSearchParams({
            source_path: router.state.location.pathname,
        }).toString()
        router.navigate(`${ROUTES.LOGIN}?${queryParamsText}`, { replace: true })
        throw new Error('请先登录')
    }

    if (data.code != 0) {
        if (!SELF_HANDLED_CODE.includes(data.code)) {
            showToast.error(data.message)
        }
        throw new CustomError(data.message, data)
    }

    return response
})


export default api
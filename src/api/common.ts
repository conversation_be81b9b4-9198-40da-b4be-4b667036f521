import { FormApi } from "@/hooks/drawer/transformers";
import api from ".";

export const getTopicList: FormApi = (params) => {
  return api.get('/client/topics/getList', { params });
};

export const getAssetList: FormApi = (params) => {
  return api.get('/client/assets/getList', { params });
};

export const getAppConfig = () => {
  return api.get('/client/app/config/getAppConfig');
};

export const createPayOrder: FormApi = ({ params }) => {
  return api.post('/client/pay/order/createOrder', params);
};

export const refreshPayQRCode: FormApi = ({ params }) => {
  return api.post('/client/pay/order/refreshQRCode', params );
};

export const cancelOrder: FormApi = ({ params }) => {
  return api.post('/client/pay/order/cancelOrder', params);
};

export const getOrderStatus: FormApi = ( params ) => {
  return api.get('/client/pay/order/getOrderStatus', params);
};

export const getOrderDetail: FormApi = ( params ) => {
  return api.get('/client/pay/order/getOrderDetail', params);
};

export const getWechatJssdkParams: FormApi = ( params ) => {
  return api.get('/client/pay/order/getWechatJssdkParams', params);
};

export const getAlipayForm: FormApi = ( params ) => {
  return api.get('/client/pay/order/getAlipayForm', params);
};

export const getToolsBotList: FormApi = ( params ) => {
  return api.get('/client/tools/bot/getList', params);
};
import { useState, useCallback } from 'react'
import { trendInsightApi } from './client'
import type {
  VideoSearchRequest,
  VideoSearchResponse,
  AuthorSearchRequest,
  AuthorSearchResponse,
  ConfigResponse,
  ConnectionTestResponse,
  ApiInfoResponse,
} from './client'

// 通用的API状态类型
interface ApiState<T> {
  data: T | null
  loading: boolean
  error: string | null
}

// 通用的API Hook
function useApiState<T>(): [
  ApiState<T>,
  (asyncFn: () => Promise<T>) => Promise<void>
] {
  const [state, setState] = useState<ApiState<T>>({
    data: null,
    loading: false,
    error: null,
  })

  const execute = useCallback(async (asyncFn: () => Promise<T>) => {
    setState(prev => ({ ...prev, loading: true, error: null }))
    
    try {
      const result = await asyncFn()
      setState({ data: result, loading: false, error: null })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      setState(prev => ({ ...prev, loading: false, error: errorMessage }))
    }
  }, [])

  return [state, execute]
}

/**
 * 视频搜索Hook
 */
export function useVideoSearch() {
  const [state, execute] = useApiState<VideoSearchResponse>()

  const searchVideos = useCallback(
    (params: VideoSearchRequest) => execute(() => trendInsightApi.searchVideos(params)),
    [execute]
  )

  return {
    ...state,
    searchVideos,
  }
}

/**
 * 创作者搜索Hook
 */
export function useAuthorSearch() {
  const [state, execute] = useApiState<AuthorSearchResponse>()

  const searchAuthors = useCallback(
    (params: AuthorSearchRequest) => execute(() => trendInsightApi.searchAuthors(params)),
    [execute]
  )

  return {
    ...state,
    searchAuthors,
  }
}

/**
 * 配置信息Hook
 */
export function useConfig() {
  const [state, execute] = useApiState<ConfigResponse>()

  const getConfig = useCallback(
    () => execute(() => trendInsightApi.getConfig()),
    [execute]
  )

  return {
    ...state,
    getConfig,
  }
}

/**
 * 连接测试Hook
 */
export function useConnectionTest() {
  const [state, execute] = useApiState<ConnectionTestResponse>()

  const testConnection = useCallback(
    () => execute(() => trendInsightApi.testConnection()),
    [execute]
  )

  return {
    ...state,
    testConnection,
  }
}

/**
 * API信息Hook
 */
export function useApiInfo() {
  const [state, execute] = useApiState<ApiInfoResponse>()

  const getApiInfo = useCallback(
    () => execute(() => trendInsightApi.getApiInfo()),
    [execute]
  )

  return {
    ...state,
    getApiInfo,
  }
}

// 组合Hook - 用于需要多个API调用的场景
export function useTrendInsight() {
  const videoSearch = useVideoSearch()
  const authorSearch = useAuthorSearch()
  const config = useConfig()
  const connectionTest = useConnectionTest()
  const apiInfo = useApiInfo()

  return {
    videoSearch,
    authorSearch,
    config,
    connectionTest,
    apiInfo,
  }
}

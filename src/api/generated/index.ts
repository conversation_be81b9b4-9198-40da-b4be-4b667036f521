// 导出所有类型定义
export type * from './types'

// 导出API客户端
export { apiClient, trendInsightApi } from './client'

// 导出类型别名
export type {
  VideoSearchRequest,
  VideoSearchResponse,
  AuthorSearchRequest,
  AuthorSearchResponse,
  ConfigResponse,
  ConnectionTestResponse,
  ApiInfoResponse,
  components,
} from './client'

// 导出React Hooks
export {
  useVideoSearch,
  useAuthorSearch,
  useConfig,
  useConnectionTest,
  useApiInfo,
  useTrendInsight,
} from './hooks'

// 导出常量和枚举
export const DATE_TYPES = {
  ALL: 0,
  LAST_7_DAYS: 1,
  LAST_30_DAYS: 2,
  LAST_90_DAYS: 3,
} as const

export const LABEL_TYPES = {
  ALL: 0,
  ORIGINAL: 1,
  REPOST: 2,
} as const

export const DURATION_TYPES = {
  ALL: 0,
  SHORT: 1,
  MEDIUM: 2,
  LONG: 3,
} as const

// 类型守卫函数
export function isVideoSearchResponse(data: any): data is any {
  return data && typeof data === 'object' && 'data' in data && 'videos' in data.data
}

export function isCreatorSearchResponse(data: any): data is any {
  return data && typeof data === 'object' && 'data' in data && 'authors' in data.data
}

export function isErrorResponse(data: any): data is { code: number; msg: string; data?: any } {
  return data && typeof data === 'object' && 'code' in data && 'msg' in data
}

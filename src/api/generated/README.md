# TrendInsight API 客户端

这个目录包含了基于 OpenAPI 规范自动生成的 TypeScript 接口访问代码。

## 文件结构

```
src/api/generated/
├── types.ts          # 自动生成的 TypeScript 类型定义
├── client.ts         # API 客户端和方法
├── hooks.ts          # React Hooks
├── index.ts          # 统一导出文件
├── examples.tsx      # 使用示例组件
└── README.md         # 本文档
```

## 安装依赖

确保已安装以下依赖：

```bash
pnpm add openapi-fetch
pnpm add -D openapi-typescript
```

## 基本使用

有两种使用方式：

### 方式一：使用项目现有的 axios 实例（推荐）

```typescript
import { trendInsightApi, DATE_TYPES, LABEL_TYPES } from '@/api/generated/integration'

// 这种方式会自动使用项目现有的认证和拦截器
const searchVideos = async () => {
  try {
    const result = await trendInsightApi.searchVideos({
      keyword: '人工智能',
      date_type: DATE_TYPES.LAST_30_DAYS,
      label_type: LABEL_TYPES.ORIGINAL,
    })
    console.log('视频搜索结果:', result)
  } catch (error) {
    console.error('搜索失败:', error)
  }
}
```

### 方式二：使用独立的 openapi-fetch 客户端

```typescript
import { trendInsightApi, DATE_TYPES, LABEL_TYPES } from '@/api/generated'

// 这种方式使用独立的HTTP客户端
const searchVideos = async () => {
  try {
    const result = await trendInsightApi.searchVideos({
      keyword: '人工智能',
      date_type: DATE_TYPES.LAST_30_DAYS,
      label_type: LABEL_TYPES.ORIGINAL,
    })
    console.log('视频搜索结果:', result)
  } catch (error) {
    console.error('搜索失败:', error)
  }
}
```

### 1. 直接使用 API 客户端

```typescript
import { trendInsightApi, DATE_TYPES, LABEL_TYPES } from '@/api/generated'

// 搜索视频
const searchVideos = async () => {
  try {
    const result = await trendInsightApi.searchVideos({
      keyword: '人工智能',
      date_type: DATE_TYPES.LAST_30_DAYS,
      label_type: LABEL_TYPES.ORIGINAL,
    })
    console.log('视频搜索结果:', result)
  } catch (error) {
    console.error('搜索失败:', error)
  }
}

// 搜索创作者
const searchCreators = async () => {
  try {
    const result = await trendInsightApi.searchCreators({
      keyword: '科技博主',
      total: 20,
    })
    console.log('创作者搜索结果:', result)
  } catch (error) {
    console.error('搜索失败:', error)
  }
}
```

### 2. 使用 React Hooks

```typescript
import { useVideoSearch, useCreatorSearch } from '@/api/generated'

function VideoSearchComponent() {
  const { data, loading, error, searchVideos } = useVideoSearch()

  const handleSearch = async () => {
    await searchVideos({
      keyword: '人工智能',
      date_type: 1, // 最近7天
    })
  }

  if (loading) return <div>搜索中...</div>
  if (error) return <div>错误: {error}</div>

  return (
    <div>
      <button onClick={handleSearch}>搜索视频</button>
      {data && (
        <div>
          <h3>搜索结果 (共 {data.data?.total} 条)</h3>
          {data.data?.videos?.map(video => (
            <div key={video.id}>
              <h4>{video.title}</h4>
              <p>作者: {video.author}</p>
              <p>播放量: {video.views}</p>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
```

### 3. 类型安全

所有的请求和响应都有完整的 TypeScript 类型支持：

```typescript
import type { 
  VideoSearchRequest, 
  VideoSearchResponse,
  CreatorSearchRequest,
  CreatorSearchResponse 
} from '@/api/generated'

// 请求参数类型
const searchParams: VideoSearchRequest = {
  keyword: '人工智能',
  author_ids: ['author1', 'author2'],
  category_id: '1',
  date_type: 1,
  label_type: 1,
  duration_type: 2,
}

// 响应数据类型
const handleResponse = (response: VideoSearchResponse) => {
  console.log('状态码:', response.code)
  console.log('消息:', response.msg)
  console.log('视频列表:', response.data?.videos)
}
```

## API 方法

### 视频搜索
- `trendInsightApi.searchVideos(params)` - 搜索视频
- `useVideoSearch()` - React Hook

### 创作者搜索
- `trendInsightApi.searchCreators(params)` - 搜索创作者
- `useCreatorSearch()` - React Hook

### 配置管理
- `trendInsightApi.getConfig()` - 获取配置信息
- `trendInsightApi.testConnection()` - 测试连接
- `trendInsightApi.getApiInfo()` - 获取API信息
- `useConfig()`, `useConnectionTest()`, `useApiInfo()` - React Hooks

## 常量定义

```typescript
import { DATE_TYPES, LABEL_TYPES, DURATION_TYPES } from '@/api/generated'

// 日期类型
DATE_TYPES.ALL          // 0 - 全部时间
DATE_TYPES.LAST_7_DAYS  // 1 - 最近7天
DATE_TYPES.LAST_30_DAYS // 2 - 最近30天
DATE_TYPES.LAST_90_DAYS // 3 - 最近90天

// 标签类型
LABEL_TYPES.ALL      // 0 - 全部标签
LABEL_TYPES.ORIGINAL // 1 - 原创
LABEL_TYPES.REPOST   // 2 - 转发

// 时长类型
DURATION_TYPES.ALL    // 0 - 全部时长
DURATION_TYPES.SHORT  // 1 - 短视频
DURATION_TYPES.MEDIUM // 2 - 中等时长
DURATION_TYPES.LONG   // 3 - 长视频
```

## 错误处理

API 客户端会自动处理错误并抛出有意义的错误消息：

```typescript
try {
  const result = await trendInsightApi.searchVideos({ keyword: '测试' })
} catch (error) {
  // error.message 包含具体的错误信息
  console.error('API 调用失败:', error.message)
}
```

## 认证

API 客户端会自动从 `useAuthStore` 获取认证令牌并添加到请求头中。确保在调用 API 之前用户已经登录。

## 重新生成类型

当 OpenAPI 规范更新时，可以重新生成类型定义：

```bash
# 获取最新的 OpenAPI 规范
curl -s http://localhost:8200/common/docs/openapi/getOpenAPISpec > openapi-spec.json

# 生成 TypeScript 类型
npx openapi-typescript openapi-spec.json -o src/api/generated/types.ts
```

## 注意事项

1. `types.ts` 文件是自动生成的，请勿手动修改
2. 其他文件可以根据项目需求进行调整
3. 确保 API 服务器正在运行并且可以访问
4. 所有 API 调用都需要有效的认证令牌

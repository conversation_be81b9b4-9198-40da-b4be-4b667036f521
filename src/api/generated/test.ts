/**
 * API 客户端测试文件
 * 
 * 这个文件用于测试生成的 API 客户端是否正常工作
 * 在实际项目中，建议使用专业的测试框架如 Jest 或 Vitest
 */

import { trendInsightApi, DATE_TYPES, LABEL_TYPES, DURATION_TYPES } from './index'
import type { VideoSearchRequest, AuthorSearchRequest } from './index'

/**
 * 测试视频搜索 API
 */
export async function testVideoSearch() {
  console.log('🧪 测试视频搜索 API...')
  
  try {
    const searchParams: VideoSearchRequest = {
      keyword: '人工智能',
      type: 'video',
      date_type: DATE_TYPES.LAST_30_DAYS,
      label_type: LABEL_TYPES.ORIGINAL,
      duration_type: DURATION_TYPES.SHORT,
    }
    
    console.log('📤 发送请求:', searchParams)
    
    const result = await trendInsightApi.searchVideos(searchParams)
    
    console.log('📥 响应结果:')
    console.log('- 状态码:', result.code)
    console.log('- 消息:', result.msg)
    console.log('- 视频总数:', result.data?.videos?.length)
    console.log('- 视频列表长度:', result.data?.videos?.length)
    
    if (result.data?.videos && result.data.videos.length > 0) {
      console.log('- 第一个视频:', {
        id: result.data.videos[0].itemId,
        title: result.data.videos[0].title,
        author: result.data.videos[0].nickname || '未知',
        likes: result.data.videos[0].likes || 0,
      })
    }
    
    console.log('✅ 视频搜索测试成功')
    return result
    
  } catch (error) {
    console.error('❌ 视频搜索测试失败:', error)
    throw error
  }
}

/**
 * 测试创作者搜索 API
 */
export async function testCreatorSearch() {
  console.log('🧪 测试创作者搜索 API...')
  
  try {
    const searchParams: AuthorSearchRequest = {
      keyword: '科技博主',
      type: 'author',
      total: 10,
    }
    
    console.log('📤 发送请求:', searchParams)
    
    const result = await trendInsightApi.searchAuthors(searchParams)
    
    console.log('📥 响应结果:')
    console.log('- 状态码:', result.code)
    console.log('- 消息:', result.msg)
    console.log('- 创作者总数:', result.data?.total)
    console.log('- 创作者列表长度:', result.data?.authors?.length)

    if (result.data?.authors && result.data.authors.length > 0) {
      console.log('- 第一个创作者:', {
        id: result.data.authors[0].user_id,
        name: result.data.authors[0].user_name,
        followers: result.data.authors[0].follow_count,
        verified: false, // 验证状态字段不存在，默认为 false
      })
    }
    
    console.log('✅ 创作者搜索测试成功')
    return result
    
  } catch (error) {
    console.error('❌ 创作者搜索测试失败:', error)
    throw error
  }
}

/**
 * 测试配置 API
 */
export async function testConfig() {
  console.log('🧪 测试配置 API...')
  
  try {
    const result = await trendInsightApi.getConfig()
    
    console.log('📥 配置信息:')
    console.log('- 状态码:', result.code)
    console.log('- 消息:', result.msg)
    console.log('- 基础URL:', result.data?.base_url)
    console.log('- 超时时间:', result.data?.timeout)
    console.log('- 重试次数:', result.data?.retry_count)
    console.log('- 端点配置:', result.data?.endpoints)
    
    console.log('✅ 配置测试成功')
    return result
    
  } catch (error) {
    console.error('❌ 配置测试失败:', error)
    throw error
  }
}

/**
 * 测试连接
 */
export async function testConnection() {
  console.log('🧪 测试连接...')
  
  try {
    const result = await trendInsightApi.testConnection()
    
    console.log('📥 连接测试结果:')
    console.log('- 状态码:', result.code)
    console.log('- 消息:', result.msg)
    console.log('- 连接状态:', result.data?.connected)
    console.log('- 基础URL:', result.data?.base_url)
    console.log('- HTTP状态码:', result.data?.status_code)
    console.log('- 响应时间:', result.data?.response_time)
    console.log('- 测试时间:', result.data?.test_time)
    
    if (result.data?.error) {
      console.log('- 错误信息:', result.data.error)
    }
    
    console.log('✅ 连接测试成功')
    return result
    
  } catch (error) {
    console.error('❌ 连接测试失败:', error)
    throw error
  }
}

/**
 * 测试 API 信息
 */
export async function testApiInfo() {
  console.log('🧪 测试 API 信息...')
  
  try {
    const result = await trendInsightApi.getApiInfo()
    
    console.log('📥 API 信息:')
    console.log('- 状态码:', result.code)
    console.log('- 消息:', result.msg)
    console.log('- 服务名称:', result.data?.service_name)
    console.log('- 版本:', result.data?.version)
    console.log('- 描述:', result.data?.description)
    console.log('- 端点数量:', result.data?.endpoints?.length)
    
    console.log('✅ API 信息测试成功')
    return result
    
  } catch (error) {
    console.error('❌ API 信息测试失败:', error)
    throw error
  }
}

/**
 * 运行所有测试
 */
export async function runAllTests() {
  console.log('🚀 开始运行所有 API 测试...')
  console.log('=' .repeat(50))
  
  const tests = [
    { name: '配置测试', fn: testConfig },
    { name: '连接测试', fn: testConnection },
    { name: 'API信息测试', fn: testApiInfo },
    { name: '视频搜索测试', fn: testVideoSearch },
    { name: '创作者搜索测试', fn: testCreatorSearch },
  ]
  
  const results = []
  
  for (const test of tests) {
    try {
      console.log(`\n📋 ${test.name}`)
      console.log('-'.repeat(30))
      
      const result = await test.fn()
      results.push({ name: test.name, success: true, result })
      
    } catch (error) {
      results.push({ name: test.name, success: false, error })
    }
  }
  
  // 输出测试总结
  console.log('\n' + '='.repeat(50))
  console.log('📊 测试总结:')
  
  const successCount = results.filter(r => r.success).length
  const totalCount = results.length
  
  console.log(`✅ 成功: ${successCount}/${totalCount}`)
  console.log(`❌ 失败: ${totalCount - successCount}/${totalCount}`)
  
  results.forEach(result => {
    const status = result.success ? '✅' : '❌'
    console.log(`${status} ${result.name}`)
    if (!result.success && result.error) {
      console.log(`   错误: ${(result.error as any)?.message || result.error}`)
    }
  })
  
  if (successCount === totalCount) {
    console.log('\n🎉 所有测试通过！')
  } else {
    console.log('\n⚠️ 部分测试失败，请检查 API 服务器状态和网络连接')
  }
  
  return results
}

// 如果直接运行此文件，执行所有测试
if (typeof window === 'undefined' && (globalThis as any).require?.main === (globalThis as any).module) {
  runAllTests().catch(console.error)
}

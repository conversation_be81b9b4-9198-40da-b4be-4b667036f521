import { useEffect, useState } from 'react'
import {
  useVideoSearch,
  useAuthorSearch,
  useConfig,
  trendInsightApi,
  DATE_TYPES,
  LABEL_TYPES,
  DURATION_TYPES,
  type VideoSearchRequest,
  type AuthorSearchRequest,
} from './index'

/**
 * 视频搜索组件示例
 */
export function VideoSearchExample() {
  const { data, loading, error, searchVideos } = useVideoSearch()
  const [searchParams, setSearchParams] = useState<VideoSearchRequest>({
    keyword: '',
    type: 'video',
  })

  const handleSearch = async () => {
    if (!searchParams.keyword.trim()) {
      alert('请输入搜索关键词')
      return
    }
    
    await searchVideos(searchParams)
  }

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">视频搜索示例</h2>
      
      {/* 搜索表单 */}
      <div className="mb-4 space-y-2">
        <input
          type="text"
          placeholder="输入搜索关键词"
          value={searchParams.keyword}
          onChange={(e) => setSearchParams(prev => ({ ...prev, keyword: e.target.value }))}
          className="w-full p-2 border rounded"
        />
        
        <div className="flex gap-2">
          <select
            value={searchParams.date_type || DATE_TYPES.ALL}
            onChange={(e) => setSearchParams(prev => ({ ...prev, date_type: Number(e.target.value) }))}
            className="p-2 border rounded"
          >
            <option value={DATE_TYPES.ALL}>全部时间</option>
            <option value={DATE_TYPES.LAST_7_DAYS}>最近7天</option>
            <option value={DATE_TYPES.LAST_30_DAYS}>最近30天</option>
            <option value={DATE_TYPES.LAST_90_DAYS}>最近90天</option>
          </select>
          
          <select
            value={searchParams.label_type || LABEL_TYPES.ALL}
            onChange={(e) => setSearchParams(prev => ({ ...prev, label_type: Number(e.target.value) }))}
            className="p-2 border rounded"
          >
            <option value={LABEL_TYPES.ALL}>全部标签</option>
            <option value={LABEL_TYPES.ORIGINAL}>原创</option>
            <option value={LABEL_TYPES.REPOST}>转发</option>
          </select>
          
          <select
            value={searchParams.duration_type || DURATION_TYPES.ALL}
            onChange={(e) => setSearchParams(prev => ({ ...prev, duration_type: Number(e.target.value) }))}
            className="p-2 border rounded"
          >
            <option value={DURATION_TYPES.ALL}>全部时长</option>
            <option value={DURATION_TYPES.SHORT}>短视频</option>
            <option value={DURATION_TYPES.MEDIUM}>中等时长</option>
            <option value={DURATION_TYPES.LONG}>长视频</option>
          </select>
        </div>
        
        <button
          onClick={handleSearch}
          disabled={loading}
          className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
        >
          {loading ? '搜索中...' : '搜索视频'}
        </button>
      </div>

      {/* 错误显示 */}
      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          错误: {error}
        </div>
      )}

      {/* 搜索结果 */}
      {data && (
        <div>
          <h3 className="text-lg font-semibold mb-2">
            搜索结果 (共 {data.data?.videos?.length || 0} 条)
          </h3>
          <div className="grid gap-4">
            {data.data?.videos?.map((video, index) => (
              <div key={video.itemId || index} className="p-4 border rounded">
                <h4 className="font-medium">{video.title}</h4>
                <p className="text-sm text-gray-600">作者: {video.nickname || '未知'}</p>
                <p className="text-sm text-gray-600">点赞数: {typeof video.likes === 'number' ? video.likes.toLocaleString() : video.likes || '未知'}</p>
                <p className="text-sm text-gray-600">时长: {video.duration}秒</p>
                <p className="text-sm text-gray-600">创建时间: {video.createTime}</p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

/**
 * 创作者搜索组件示例
 */
export function CreatorSearchExample() {
  const { data, loading, error, searchAuthors } = useAuthorSearch()
  const [keyword, setKeyword] = useState('')
  const [total, setTotal] = useState(20)

  const handleSearch = async () => {
    if (!keyword.trim()) {
      alert('请输入搜索关键词')
      return
    }
    
    const params: AuthorSearchRequest = { keyword, type: 'author' }
    if (total > 0) {
      params.total = total
    }
    
    await searchAuthors(params)
  }

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">创作者搜索示例</h2>
      
      <div className="mb-4 space-y-2">
        <input
          type="text"
          placeholder="输入搜索关键词"
          value={keyword}
          onChange={(e) => setKeyword(e.target.value)}
          className="w-full p-2 border rounded"
        />
        
        <div className="flex gap-2">
          <input
            type="number"
            placeholder="返回数量"
            value={total}
            onChange={(e) => setTotal(Number(e.target.value))}
            min="1"
            max="100"
            className="p-2 border rounded"
          />
          
          <button
            onClick={handleSearch}
            disabled={loading}
            className="px-4 py-2 bg-green-500 text-white rounded disabled:opacity-50"
          >
            {loading ? '搜索中...' : '搜索创作者'}
          </button>
        </div>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          错误: {error}
        </div>
      )}

      {data && (
        <div>
          <h3 className="text-lg font-semibold mb-2">
            搜索结果 (共 {data.data?.total || 0} 条)
          </h3>
          <div className="grid gap-4">
            {data.data?.authors?.map((creator: any, index: number) => (
              <div key={creator.id || index} className="p-4 border rounded">
                <div className="flex items-center gap-3">
                  {creator.avatar && (
                    <img
                      src={creator.avatar}
                      alt={creator.name}
                      className="w-12 h-12 rounded-full"
                    />
                  )}
                  <div>
                    <h4 className="font-medium">{creator.name}</h4>
                    <p className="text-sm text-gray-600">
                      粉丝: {creator.followers?.toLocaleString()} | 
                      视频: {creator.videos?.toLocaleString()} | 
                      点赞: {creator.likes?.toLocaleString()}
                    </p>
                    {creator.verified && (
                      <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                        已认证
                      </span>
                    )}
                  </div>
                </div>
                {creator.description && (
                  <p className="text-sm mt-2">{creator.description}</p>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

/**
 * 配置信息组件示例
 */
export function ConfigExample() {
  const { data, loading, error, getConfig } = useConfig()

  useEffect(() => {
    getConfig()
  }, [getConfig])

  if (loading) return <div>加载配置信息中...</div>
  if (error) return <div>错误: {error}</div>

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">配置信息</h2>
      {data && (
        <div className="space-y-2">
          <p><strong>基础URL:</strong> {data.data?.base_url}</p>
          <p><strong>超时时间:</strong> {data.data?.timeout}</p>
          <p><strong>重试次数:</strong> {data.data?.retry_count}</p>
          
          {data.data?.endpoints && (
            <div>
              <h3 className="font-semibold mt-4">API端点:</h3>
              <ul className="list-disc list-inside">
                <li>视频搜索: {data.data.endpoints.search_videos}</li>
                <li>创作者搜索: {data.data.endpoints.search_creators}</li>
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

/**
 * 直接使用API客户端的示例
 */
export async function directApiExample() {
  try {
    // 直接调用API方法
    const videoResult = await trendInsightApi.searchVideos({
      keyword: '人工智能',
      type: 'video',
      date_type: DATE_TYPES.LAST_30_DAYS,
      label_type: LABEL_TYPES.ORIGINAL,
    })
    
    console.log('视频搜索结果:', videoResult)
    
    const creatorResult = await trendInsightApi.searchAuthors({
      keyword: '科技博主',
      type: 'author',
      total: 10,
    })
    
    console.log('创作者搜索结果:', creatorResult)
    
    const config = await trendInsightApi.getConfig()
    console.log('配置信息:', config)
    
  } catch (error) {
    console.error('API调用失败:', error)
  }
}

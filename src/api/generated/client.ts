import createClient from 'openapi-fetch'
import type { paths } from './types'
import { useAuthStore } from '@/models/auth'
import { router } from '@/routes'
import { ROUTES } from '@/routes/constants'
import { showToast } from '@/utils/toast'
import { SELF_HANDLED_CODE } from '@/api/code'
import { CustomError } from '@/error'

// 获取认证令牌的函数
const getAuthToken = () => {
  try {
    // 优先从 useAuthStore 获取 token
    const { state } = useAuthStore.getState()
    if (state.token) {
      return state.token
    }
  } catch (error) {
    console.warn('获取认证令牌失败:', error)
  }
  return null
}

// 创建基础API客户端
export const apiClient = createClient<paths>({
  baseUrl: import.meta.env.VITE_API_URL,
})

// 添加请求和响应拦截器
apiClient.use({
  onRequest({ request }) {
    // 添加认证 token
    const token = getAuthToken()
    if (token) {
      request.headers.set('Authorization', token)
    }
    return request
  },

  async onResponse({ response }) {
    // 处理响应数据
    try {
      // 克隆响应以便多次读取
      const clonedResponse = response.clone()
      const data = await clonedResponse.json()

      // 如果没有 code 字段或 code 为 0，表示成功
      if (!data.code || data.code === 0) {
        return response
      }

      // 处理 401 未授权错误
      if (data.code === 401) {
        const queryParamsText = new URLSearchParams({
          source_path: router.state.location.pathname,
        }).toString()
        router.navigate(`${ROUTES.LOGIN}?${queryParamsText}`, { replace: true })
        throw new CustomError('请先登录', data)
      }

      // 处理其他错误码
      if (data.code !== 0) {
        // 如果不是自处理的错误码，显示错误提示
        if (!SELF_HANDLED_CODE.includes(data.code)) {
          showToast.error(data.msg || data.message || '请求失败')
        }
        throw new CustomError(data.msg || data.message || '请求失败', data)
      }

      return response
    } catch (error) {
      // 如果不是 JSON 响应或解析失败，直接返回原响应
      if (error instanceof CustomError) {
        throw error
      }
      return response
    }
  },
})

// 导出类型安全的API方法
export const trendInsightApi = {
  /**
   * 搜索巨量引擎平台视频
   * @param params 搜索参数
   * @returns 视频搜索结果
   */
  async searchVideos(params: paths['/client/trendinsight/searchVideos']['post']['requestBody']['content']['application/json']) {
    const { data, error } = await apiClient.POST('/client/trendinsight/searchVideos', {
      body: params,
    })
    
    if (error) {
      throw new Error(error.msg || '视频搜索失败')
    }
    
    return data
  },

  /**
   * 搜索巨量引擎平台创作者
   * @param params 搜索参数
   * @returns 创作者搜索结果
   */
  async searchAuthors(params: paths['/client/trendinsight/searchAuthors']['post']['requestBody']['content']['application/json']) {
      const { data, error } = await apiClient.POST('/client/trendinsight/searchAuthors', {
        body: params,
      })

    if (error) {
      throw new Error(error.msg || '创作者搜索失败')
    }

    return data
  },

  /**
   * 获取TrendInsight配置信息
   * @returns 配置信息
   */
  async getConfig() {
    const { data, error } = await apiClient.GET('/client/trendinsight/config/getConfig')
    
    if (error) {
      throw new Error(error.msg || '获取配置信息失败')
    }
    
    return data
  },

  /**
   * 测试TrendInsight连接
   * @returns 连接测试结果
   */
  async testConnection() {
    const { data, error } = await apiClient.POST('/client/trendinsight/config/testConnection')
    
    if (error) {
      throw new Error(error.msg || '连接测试失败')
    }
    
    return data
  },

  /**
   * 获取API信息
   * @returns API信息
   */
  async getApiInfo() {
    const { data, error } = await apiClient.GET('/client/trendinsight/config/getApiInfo')
    
    if (error) {
      throw new Error(error.msg || '获取API信息失败')
    }
    
    return data
  },

  /**
   * 获取作者视频列表
   * @param params 搜索参数
   * @returns 作者视频列表
   */
  async getAuthorVideos(params: paths['/client/user/douyin/getAuthorVideos']['get']['parameters']['query']) {
    const { data, error } = await apiClient.GET('/client/user/douyin/getAuthorVideos', {
      params: { query: params },
    })

    if (error) {
      throw new Error(error.msg || '获取作者视频失败')
    }

    return data
  },

  /**
   * 获取用户视频关键词监控列表
   */
  async getUserVideoKeywords(params: paths['/client/trendinsight/getUserVideoKeywords']['get']['parameters']['query']) {
    const { data, error } = await apiClient.GET('/client/trendinsight/getUserVideoKeywords', {
      params: { query: params },
    })
    if (error) {
      throw new Error(error.msg || '获取视频关键词列表失败')
    }
    return data
  },

  /**
   * 获取用户作者关键词监控列表
   */
  async getUserAuthorKeywords(params: paths['/client/trendinsight/getUserAuthorKeywords']['get']['parameters']['query']) {
    const { data, error } = await apiClient.GET('/client/trendinsight/getUserAuthorKeywords', {
      params: { query: params },
    })
    if (error) {
      throw new Error(error.msg || '获取作者关键词列表失败')
    }
    return data
  },

  /**
   * 创建或更新视频关键词
   */
  async upsertVideoKeywords(params: paths['/client/trendinsight/upsertVideoKeywords']['post']['requestBody']['content']['application/json']) {
    const { data, error } = await apiClient.POST('/client/trendinsight/upsertVideoKeywords', {
      body: params,
    })
    if (error) {
      throw new Error(error.msg || '视频关键词创建/更新失败')
    }
    return data
  },

  /**
   * 创建或更新作者关键词
   */
  async upsertAuthorKeywords(params: paths['/client/trendinsight/upsertAuthorKeywords']['post']['requestBody']['content']['application/json']) {
    const { data, error } = await apiClient.POST('/client/trendinsight/upsertAuthorKeywords', {
      body: params,
    })
    if (error) {
      throw new Error(error.msg || '作者关键词创建/更新失败')
    }
    return data
  },

  /**
   * 删除用户关键词关联
   */
  async deleteUserKeyword(keywordUuid: string) {
    const { data, error } = await apiClient.DELETE('/client/trendinsight/delUserKeywordParId/{id}', {
      params: { path: { id: keywordUuid } },
    })
    if (error) {
      throw new Error(error.msg || '删除关键词失败')
    }
    return data
  },

  /**
   * 获取用户视频关键词统计信息
   */
  async getVideoKeywordStats() {
    const { data, error } = await apiClient.GET('/client/trendinsight/getVideoKeywordStats')
    if (error) {
      throw new Error(error.msg || '获取视频关键词统计失败')
    }
    return data
  },

  /**
   * 获取用户作者关键词统计信息
   */
  async getAuthorKeywordStats() {
    const { data, error } = await apiClient.GET('/client/trendinsight/getAuthorKeywordStats')
    if (error) {
      throw new Error(error.msg || '获取作者关键词统计失败')
    }
    return data
  },
}

export const douyinApi = {}

// 导出类型定义，方便在其他地方使用
export type VideoSearchRequest = paths['/client/trendinsight/searchVideos']['post']['requestBody']['content']['application/json']
export type VideoSearchResponse = paths['/client/trendinsight/searchVideos']['post']['responses']['200']['content']['application/json']
export type AuthorSearchRequest = paths['/client/trendinsight/searchAuthors']['post']['requestBody']['content']['application/json']
export type AuthorSearchResponse = paths['/client/trendinsight/searchAuthors']['post']['responses']['200']['content']['application/json']
export type ConfigResponse = paths['/client/trendinsight/config/getConfig']['get']['responses']['200']['content']['application/json']
// 导出组件类型
 export type { components, paths } from './types'
 
 export type ConnectionTestResponse = paths['/client/trendinsight/config/testConnection']['post']['responses']['200']['content']['application/json']
 export type ApiInfoResponse = paths['/client/trendinsight/config/getApiInfo']['get']['responses']['200']['content']['application/json']

// 常量定义
export const DATE_TYPES = {
  ALL: 0,
  LAST_7_DAYS: 1,
  LAST_30_DAYS: 2,
  LAST_90_DAYS: 3,
} as const

export const LABEL_TYPES = {
  ALL: 0,
  ORIGINAL: 1,
  REPOST: 2,
} as const

export const DURATION_TYPES = {
  ALL: 0,
  SHORT: 1,
  MEDIUM: 2,
  LONG: 3,
} as const

// 类型守卫
export function isVideoSearchResponse(data: any): data is VideoSearchResponse {
  return data && typeof data === 'object' && 'data' in data && data.data && 'videos' in data.data
}

export function isAuthorSearchResponse(data: any): data is AuthorSearchResponse {
  return data && typeof data === 'object' && 'data' in data && data.data && 'authors' in data.data
}

export function isErrorResponse(data: any): data is { code: number, msg: string } {
  return data && typeof data === 'object' && 'code' in data && data.code !== 0 && 'msg' in data
}

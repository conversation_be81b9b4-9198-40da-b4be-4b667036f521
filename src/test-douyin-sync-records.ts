// 测试抖音同步记录 API 调用
import { apiClient } from '@/api/generated/client';

export async function testDouyinSyncRecords() {
  console.log('🔍 测试抖音同步记录 API...');
  
  try {
    const { data, error } = await apiClient.GET('/client/user/douyin/getSyncRecords', {
      params: {
        query: {
          page: 1,
          page_size: 10
        }
      }
    });

    if (error) {
      console.error('❌ API 调用失败:', error);
      return { success: false, error: error.msg || '未知错误' };
    }

    if (data?.code === 0) {
      console.log('✅ API 调用成功:', {
        records: data.data?.records?.length || 0,
        pagination: data.data?.pagination
      });
      return { 
        success: true, 
        data: {
          records: data.data?.records || [],
          pagination: data.data?.pagination
        }
      };
    } else {
      console.error('❌ API 返回错误:', data);
      return { success: false, error: data?.msg || '未知错误' };
    }
  } catch (error: any) {
    console.error('❌ 网络错误:', error);
    return { success: false, error: error.message || '网络错误' };
  }
}

// 在浏览器控制台中可以调用这个函数进行测试
if (typeof window !== 'undefined') {
  (window as any).testDouyinSyncRecords = testDouyinSyncRecords;
}

import { createBrowserRouter, Navigate, Outlet } from 'react-router-dom';
import WorkBench from '../pages/Dashboard';
import Inbox from '../pages/Inbox';
import DouyinCollection from '../pages/DouyinCollection';
import AccountMonitoring from '../pages/AccountMonitoring';
import KeywordMonitoring from '../pages/KeywordMonitoring';
import Assets from '../pages/Assets';
import Topics from '../pages/Topics';
import Tools from '../pages/Tools';
import Login from '../pages/Login';
import Layout from '../components/Layout';
import ProtectedRoute from '../components/ProtectedRoute';
import { ROUTES } from './constants';
import WXLogin from '../pages/pretreat/WXLogin';
import WXOffiaccountLogin from '@/pages/pretreat/WXOffiaccountLogin';
import UserAgreement from '../pages/agreement/UserAgreement';
import PrivacyPolicy from '../pages/agreement/PrivacyPolicy';
import Price from '../pages/Price';
import WeChatPayment from '../pages/pay/WeChatPayment';
import PaymentSuccess from '../pages/pay/PaymentSuccess';
import ToastDemo from '../pages/ToastDemo';

// 创建一个包含AuthProvider的根布局
const AuthLayout = () => {
  return (
    <Outlet />
  );
};

export const router = createBrowserRouter([
  {
    element: <AuthLayout />,
    children: [
      {
        path: ROUTES.HOME,
        element: <Navigate to={ROUTES.DASHBOARD} replace />,
        handle: { title: '工作台' }
      },
      {
        element: <ProtectedRoute />,
        children: [
          {
            element: <Layout />,
            children: [
              {
                path: ROUTES.DASHBOARD,
                element: <WorkBench />,
                handle: { title: '工作台' }
              },
              {
                path: ROUTES.INBOX,
                element: <Inbox />,
                handle: { title: '灵感收件箱' }
              },
              {
                path: ROUTES.DOUYIN_COLLECTION,
                element: <DouyinCollection />,
                handle: { title: '同步抖音收藏夹' }
              },
              {
                path: ROUTES.ACCOUNT_MONITORING,
                element: <AccountMonitoring />,
                handle: { title: '对标账号监控' }
              },
              {
                path: ROUTES.KEYWORD_MONITORING,
                element: <KeywordMonitoring />,
                handle: { title: '行业关键词监控' }
              },
              {
                path: ROUTES.ASSETS,
                element: <Assets />,
                handle: { title: '素材库' }
              },
              {
                path: ROUTES.TOPICS,
                element: <Topics />,
                handle: { title: '选题库' }
              },
              {
                path: ROUTES.TOOLS,
                element: <Tools />,
                handle: { title: '智能体' }
              },
              {
                path: ROUTES.PRICE,
                element: <Price />,
                handle: { title: '起号助手' }
              },
              {
                path: ROUTES.TOAST_DEMO,
                element: <ToastDemo />,
                handle: { title: 'Toast 演示' }
              }
            ]
          }
        ]
      }
    ]
  },
  {
    path: ROUTES.LOGIN,
    element: <Login />,
    handle: { title: '登录' }
  },
  {
    path: ROUTES.PAY,
    children: [
      {
        path: ROUTES.WECHAT_PAYMENT,
        element: <WeChatPayment />,
        handle: { title: '起号助手' }
      },
      {
        path: ROUTES.PAYMENT_SUCCESS,
        element: <PaymentSuccess />,
        handle: { title: '起号助手' }
      },
    ]
  },
  {
    path: ROUTES.AGREEMENT,
    children: [
      {
        path: ROUTES.USER_AGREEMENT,
        element: <UserAgreement />,
        handle: { title: '起号助手' }
      },
      {
        path: ROUTES.PRIVACY_POLICY,
        element: <PrivacyPolicy />,
        handle: { title: '起号助手' }
      },
    ]
  },
  {
    path: ROUTES.PRETREAT,
    children: [
      {
        path: ROUTES.WX_LOGIN,
        element: <WXLogin />
      },
      {
        path: ROUTES.WX_OFFIACCOUNT_LOGIN,
        element: <WXOffiaccountLogin />
      }
    ]
  }
], {
  basename: import.meta.env.VITE_APP_BASENAME
}); 
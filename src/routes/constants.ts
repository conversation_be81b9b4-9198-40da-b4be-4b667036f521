export enum ROUTES {
  HOME = '/',
  LOGIN = '/login',
  DASHBOARD = '/dashboard',
  INBOX = '/inbox',
  DOUYIN_COLLECTION = '/inbox/douyin-collection',
  ACCOUNT_MONITORING = '/inbox/account-monitoring',
  KEYWORD_MONITORING = '/inbox/keyword-monitoring',
  ASSETS = '/assets',
  TOPICS = '/topics',
  TOOLS = '/tools',
  PRICE = '/price',
  PRETREAT = '/pretreat',
  WX_LOGIN = '/pretreat/wx-login',
  WX_OFFIACCOUNT_LOGIN = '/pretreat/wx-offiaccount-login',
  AGREEMENT = '/agreement',
  USER_AGREEMENT = '/agreement/user-agreement',
  PRIVACY_POLICY = '/agreement/privacy-policy',
  PAY = '/pay',
  WECHAT_PAYMENT = '/pay/wechat',
  PAYMENT_SUCCESS = '/pay/success',
  TOAST_DEMO = '/toast-demo',
}

export const FREE_ROUTES = [
  ROUTES.HOME,
  ROUTES.DASHBOARD,
  ROUTES.INBOX,
  ROUTES.DOUYIN_COLLECTION,
  ROUTES.ACCOUNT_MONITORING,
  ROUTES.KEYWORD_MONITORING,
  ROUTES.TOOLS,
  ROUTES.PRICE,
  ROUTES.LOGIN,
  ROUTES.WX_LOGIN,
  ROUTES.WX_OFFIACCOUNT_LOGIN,
  ROUTES.PRETREAT,
  ROUTES.USER_AGREEMENT,
  ROUTES.PRIVACY_POLICY,
  ROUTES.AGREEMENT,
  ROUTES.PAY,
  ROUTES.WECHAT_PAYMENT,
  ROUTES.PAYMENT_SUCCESS,
  ROUTES.TOAST_DEMO
]

import React, { useMemo, useRef } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ROUTES } from '../routes/constants';
import { <PERSON><PERSON>, Drawer, <PERSON>u, Modal } from 'react-daisyui';
import clsx from 'clsx';
import { useAuthStore } from '@/models/auth';
import { ROLES } from '@/cons';
import { useUserStore } from '@/models/user';
import { useInboxStore } from '@/models/inbox';

interface SubMenuItem {
  path: string;
  label: string;
  iconClass: string;
}

interface MenuItem {
  path: string;
  label: string;
  iconClass: string;
  getValue?: (stats: any) => string;
  subItems?: SubMenuItem[];
}

interface SideMenuProps {
  visible?: boolean;
  closeDrawer?: () => void;
}

export const SideMenu: React.FC<SideMenuProps> = ({ visible = false, closeDrawer }) => {
  const location = useLocation();
  const { state, actions } = useAuthStore();
  const logoutRef = useRef<any>(null);
  const userRole = useMemo(() => state.userInfo?.role, [state.userInfo?.role]);
  const { state: userState } = useUserStore();

  // 获取 inbox 统计数据
  const syncStats = useInboxStore((state) => state.state.syncStats);
  const videoRelatedStats = useInboxStore((state) => state.state.videoRelatedStats);

  // 计算 inbox 总数的函数，与 Inbox.tsx 中的逻辑保持一致
  const getInboxTotal = () => {
    // 使用与 Inbox.tsx 相同的逻辑：videoRelatedStats?.total || syncStats?.total_synced_videos || 0
    const total = videoRelatedStats?.total || syncStats?.total_synced_videos || 0;
    // 大于 9999 显示 9999+
    return total > 9999 ? '9999+' : String(total);
  };

  const menuItems: MenuItem[] = [
    { path: ROUTES.DASHBOARD, label: '工作台', iconClass: 'fas fa-home !mr-0.5' },
    {
      path: ROUTES.INBOX,
      label: '灵感收件箱',
      iconClass: 'fas fa-inbox !mr-0.5',
      getValue: () => getInboxTotal(),
      subItems: [
        { path: ROUTES.DOUYIN_COLLECTION, label: '同步抖音收藏夹', iconClass: 'fas fa-star' },
        { path: ROUTES.ACCOUNT_MONITORING, label: '对标账号监控', iconClass: 'fas fa-eye' },
        { path: ROUTES.KEYWORD_MONITORING, label: '行业关键词监控', iconClass: 'fas fa-search' },
      ]
    },
    { path: ROUTES.ASSETS, label: '素材库', iconClass: 'fas fa-photo-film !mr-0.5', getValue: (stats: any) => stats.asset_count },
    { path: ROUTES.TOPICS, label: '选题库', iconClass: 'fas fa-lightbulb !mr-0.5', getValue: (stats: any) => stats.topic_count },
    { path: ROUTES.TOOLS, label: '智能体', iconClass: 'fas fa-robot !mr-0.5' },
  ];

  const isMenuItemDisabled = (path: string) => {
    if (userRole === ROLES.FREE) {
      return path !== ROUTES.DASHBOARD;
    }
    if (userRole === ROLES.LIMITED) {
      return path !== ROUTES.TOOLS;
    }
    return false;
  };

  const isCurrentPath = (path: string) => location.pathname === path;
  const isParentActive = (menuItem: MenuItem) => {
    return isCurrentPath(menuItem.path) ||
           (menuItem.subItems?.some(subItem => isCurrentPath(subItem.path)) ?? false);
  };

  return (
    <div>
      <Drawer className="lg:drawer-open" open={visible} onClickOverlay={closeDrawer} side={(
        <div className="bg-base-100 w-64 min-h-full p-4 flex flex-col">
          <Menu className="gap-2 flex-grow w-full" size="lg">
            {menuItems.map(menuItem => (
              <div key={menuItem.path} className="w-full">
                <Menu.Item className={clsx('w-full', { 'menu-disabled': isMenuItemDisabled(menuItem.path) })}>
                  {menuItem.subItems ? (
                    // 有子菜单的项目 - 直接显示父级链接
                    <Link className={clsx('menu-item w-full flex justify-between items-center', {
                      'menu-active font-bold': isParentActive(menuItem),
                    })} to={menuItem.path}>
                      <div className="flex items-center flex-1">
                        <i className={menuItem.iconClass}></i>
                        <span>{menuItem.label}</span>
                      </div>
                      {menuItem.getValue && (
                        <div className={clsx('badge badge-sm rounded-xl', {
                          'badge-neutral': isParentActive(menuItem),
                          'badge-ghost': !isParentActive(menuItem)
                        })}>
                          {menuItem.getValue(userState.dataStats)}
                        </div>
                      )}
                    </Link>
                  ) : (
                    // 没有子菜单的项目
                    <Link className={clsx('menu-item w-full flex justify-between items-center', {
                      'menu-active font-bold': isCurrentPath(menuItem.path),
                    })} to={menuItem.path}>
                      <div className="flex items-center flex-1">
                        <i className={menuItem.iconClass}></i>
                        <span>{menuItem.label}</span>
                      </div>
                      {menuItem.getValue && (
                        <div className={clsx('badge badge-sm rounded-xl', {
                          'badge-neutral': isCurrentPath(menuItem.path),
                          'badge-ghost': !isCurrentPath(menuItem.path)
                        })}>
                          {menuItem.getValue(userState.dataStats)}
                        </div>
                      )}
                    </Link>
                  )}
                </Menu.Item>

                {/* 子菜单 - 始终显示 */}
                {menuItem.subItems && (
                  <div className="ml-6 mt-2 space-y-1 border-l-2 border-base-300 pl-4">
                    {menuItem.subItems.map(subItem => (
                      <div key={subItem.path}>
                        <Link
                          className={clsx('submenu-item flex items-center text-sm py-2 px-3 rounded-lg transition-all duration-200', {
                            'bg-base-200 font-medium text-base-content': isCurrentPath(subItem.path),
                            'text-base-content opacity-70 hover:opacity-100 hover:bg-base-100': !isCurrentPath(subItem.path),
                          })}
                          to={subItem.path}
                        >
                          <i className={clsx(subItem.iconClass, 'mr-2 text-xs')}></i>
                          <span>{subItem.label}</span>
                        </Link>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </Menu>

          {userRole === ROLES.FREE && (
            <div className="mt-4 mb-2">
              <Link to={ROUTES.PRICE} className="btn btn-outline btn-block">
                <i className="fas fa-crown mr-2"></i>升级为付费版
              </Link>
            </div>
          )}

          <div className="user-profile flex items-center justify-between p-3 mt-2 border-t border-base-300">
            <div className="flex items-center">
              <div className="avatar">
                <div className="w-10 h-10 rounded-full">
                  <img src={state.userInfo?.wx_avatar} alt="用户头像" />
                </div>
              </div>
              <div className="ml-3">
                <p className="font-medium">{state.userInfo?.wx_nickname}</p>
                <p className="text-xs opacity-60">创作者</p>
              </div>
            </div>
            <button className="btn btn-sm btn-ghost btn-circle" onClick={() => logoutRef.current.showModal()}>
              <i className="fas fa-sign-out-alt"></i>
            </button>
          </div>
        </div>
      )}>
      </Drawer>

      <Modal ref={logoutRef} containerClasses="modal-bottom sm:modal-middle">
        <Modal.Header className="text-lg font-bold mb-4">确认退出</Modal.Header>
        <Modal.Body>
          <p>您确定要退出登录吗？</p>
        </Modal.Body>
        <Modal.Actions>
          <form method="dialog" className="flex gap-2">
            <Button>取消</Button>
            <Button color="error" className="text-white" onClick={() => actions.logout()}>确认退出</Button>
          </form>
        </Modal.Actions>
      </Modal>
    </div>
  );
}; 
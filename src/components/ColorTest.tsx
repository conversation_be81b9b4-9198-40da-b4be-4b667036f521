import React from 'react';

const ColorTest: React.FC = () => {
    const getComputedStyleHsl = (varName: string) => {
        const element = document.createElement('div');
        document.body.appendChild(element);
        element.style.color = `hsl(var(${varName}))`;
        const computedColor = window.getComputedStyle(element).color;
        document.body.removeChild(element);
        return computedColor;
    };

    const getComputedStyleOklch = (varName: string) => {
        const element = document.createElement('div');
        document.body.appendChild(element);
        element.style.color = `oklch(var(${varName}))`;
        const computedColor = window.getComputedStyle(element).color;
        document.body.removeChild(element);
        return computedColor;
    };

    const checkVariable = (varName: string, useOklch = false) => {
        try {
            return useOklch ? getComputedStyleOklch(varName) : getComputedStyleHsl(varName);
        } catch (error) {
            return 'undefined';
        }
    };

    return (
        <div className="p-4 space-y-4">
            <h2 className="text-xl font-bold">DaisyUI 颜色变量测试</h2>

            <div className="grid grid-cols-1 gap-6">
                <div>
                    <h3 className="font-semibold mb-4 text-lg">成功色 (Success) - HSL vs OKLCH 对比</h3>
                    <div className="grid grid-cols-2 gap-4">
                        <div>
                            <h4 className="font-medium mb-2">使用 HSL</h4>
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <div className="w-6 h-6 rounded border" style={{ backgroundColor: 'hsl(var(--su))' }}></div>
                                    <span className="text-sm">--su: {checkVariable('--su', false)}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <div className="w-6 h-6 rounded border" style={{ backgroundColor: 'hsl(var(--suc))' }}></div>
                                    <span className="text-sm">--suc: {checkVariable('--suc', false)}</span>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h4 className="font-medium mb-2">使用 OKLCH</h4>
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <div className="w-6 h-6 rounded border" style={{ backgroundColor: 'oklch(var(--su))' }}></div>
                                    <span className="text-sm">--su: {checkVariable('--su', true)}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <div className="w-6 h-6 rounded border" style={{ backgroundColor: 'oklch(var(--suc))' }}></div>
                                    <span className="text-sm">--suc: {checkVariable('--suc', true)}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    <h3 className="font-semibold mb-4 text-lg">错误色 (Error) - HSL vs OKLCH 对比</h3>
                    <div className="grid grid-cols-2 gap-4">
                        <div>
                            <h4 className="font-medium mb-2">使用 HSL</h4>
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <div className="w-6 h-6 rounded border" style={{ backgroundColor: 'hsl(var(--er))' }}></div>
                                    <span className="text-sm">--er: {checkVariable('--er', false)}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <div className="w-6 h-6 rounded border" style={{ backgroundColor: 'hsl(var(--erc))' }}></div>
                                    <span className="text-sm">--erc: {checkVariable('--erc', false)}</span>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h4 className="font-medium mb-2">使用 OKLCH</h4>
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <div className="w-6 h-6 rounded border" style={{ backgroundColor: 'oklch(var(--er))' }}></div>
                                    <span className="text-sm">--er: {checkVariable('--er', true)}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <div className="w-6 h-6 rounded border" style={{ backgroundColor: 'oklch(var(--erc))' }}></div>
                                    <span className="text-sm">--erc: {checkVariable('--erc', true)}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    <h3 className="font-semibold mb-4 text-lg">警告色 (Warning) - HSL vs OKLCH 对比</h3>
                    <div className="grid grid-cols-2 gap-4">
                        <div>
                            <h4 className="font-medium mb-2">使用 HSL</h4>
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <div className="w-6 h-6 rounded border" style={{ backgroundColor: 'hsl(var(--wa))' }}></div>
                                    <span className="text-sm">--wa: {checkVariable('--wa', false)}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <div className="w-6 h-6 rounded border" style={{ backgroundColor: 'hsl(var(--wac))' }}></div>
                                    <span className="text-sm">--wac: {checkVariable('--wac', false)}</span>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h4 className="font-medium mb-2">使用 OKLCH</h4>
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <div className="w-6 h-6 rounded border" style={{ backgroundColor: 'oklch(var(--wa))' }}></div>
                                    <span className="text-sm">--wa: {checkVariable('--wa', true)}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <div className="w-6 h-6 rounded border" style={{ backgroundColor: 'oklch(var(--wac))' }}></div>
                                    <span className="text-sm">--wac: {checkVariable('--wac', true)}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    <h3 className="font-semibold mb-4 text-lg">信息色 (Info) - HSL vs OKLCH 对比</h3>
                    <div className="grid grid-cols-2 gap-4">
                        <div>
                            <h4 className="font-medium mb-2">使用 HSL</h4>
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <div className="w-6 h-6 rounded border" style={{ backgroundColor: 'hsl(var(--in))' }}></div>
                                    <span className="text-sm">--in: {checkVariable('--in', false)}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <div className="w-6 h-6 rounded border" style={{ backgroundColor: 'hsl(var(--inc))' }}></div>
                                    <span className="text-sm">--inc: {checkVariable('--inc', false)}</span>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h4 className="font-medium mb-2">使用 OKLCH</h4>
                            <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                    <div className="w-6 h-6 rounded border" style={{ backgroundColor: 'oklch(var(--in))' }}></div>
                                    <span className="text-sm">--in: {checkVariable('--in', true)}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <div className="w-6 h-6 rounded border" style={{ backgroundColor: 'oklch(var(--inc))' }}></div>
                                    <span className="text-sm">--inc: {checkVariable('--inc', true)}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div className="mt-8">
                <h3 className="font-semibold mb-2">DaisyUI Alert 组件测试</h3>
                <div className="space-y-4">
                    <div className="alert alert-success">
                        <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span>DaisyUI 成功提示样式</span>
                    </div>
                    <div className="alert alert-error">
                        <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span>DaisyUI 错误提示样式</span>
                    </div>
                    <div className="alert alert-warning">
                        <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                        <span>DaisyUI 警告提示样式</span>
                    </div>
                    <div className="alert alert-info">
                        <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span>DaisyUI 信息提示样式</span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ColorTest;

import React from 'react';

interface CardProps {
  title: string;
  status: 'pending' | 'completed';
}

export const Card: React.FC<CardProps> = ({ title, status }) => {
  return (
    <div className="card bg-base-200 hover:shadow-lg transition-shadow">
      <div className="card-body">
        <h2 className="card-title">
          {title}
          <span className="text-sm font-normal">
            （{status === 'pending' ? '待完善' : '已完善'}）
          </span>
        </h2>
        <div className="card-actions justify-end">
          <button className="btn btn-primary">查看详情</button>
        </div>
      </div>
    </div>
  );
}; 
import { Fragment } from 'react'
import {
    useReactTable,
    getCoreRowModel,
    getExpandedRowModel,
    ColumnDef,
    flexRender,
} from '@tanstack/react-table'
import clsx from 'clsx'

type TableProps<TData> = {
    data: TData[]
    columns: ColumnDef<TData>[]
    onRowClick?: (data: TData, index: number) => void
    className?: string
}

export default function Table({
    data,
    columns,
    className,
    onRowClick,
}: TableProps<any>): JSX.Element {
    const table = useReactTable<any>({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
        getExpandedRowModel: getExpandedRowModel(),
        enableColumnResizing: true,
        defaultColumn: {
            minSize: 2,
            size: 16,
            maxSize: 400,
        },
    })

    return (

        <div className={className}>
            <table
                className={clsx("table bg-base-100")}
                style={{ minWidth: `${table.getCenterTotalSize()}px` }}
            >
                <thead>
                    {table.getHeaderGroups().map(headerGroup => (
                        <tr key={headerGroup.id}>
                            {headerGroup.headers.map(header => {
                                return (
                                    <th key={header.id} colSpan={header.colSpan} style={{ minWidth: `${header.getSize()}px` }}>
                                        {header.isPlaceholder ? null : (
                                            <div>
                                                {flexRender(
                                                    header.column.columnDef.header,
                                                    header.getContext()
                                                )}
                                            </div>
                                        )}
                                    </th>
                                )
                            })}
                        </tr>
                    ))}
                </thead>
                <tbody>
                    {table.getRowModel().rows.map((row, index) => {
                        return (
                            <Fragment key={row.id}>
                                <tr
                                    className="hover:bg-base-300 cursor-pointer"
                                    onClick={() => { onRowClick?.(row.original, index) }}
                                >
                                    {/* first row is a normal row */}
                                    {row.getVisibleCells().map(cell => {
                                        return (
                                            <td key={cell.id} style={{ minWidth: `${cell.column.getSize()}px` }}>
                                                {flexRender(
                                                    cell.column.columnDef.cell,
                                                    cell.getContext()
                                                )}
                                            </td>
                                        )
                                    })}
                                </tr>
                            </Fragment>
                        )
                    })}
                </tbody>
            </table>
            {/* <div className="h-2" /> */}
            {/* <div>{table.getRowModel().rows.length} Rows</div> */}
        </div>
    )
}

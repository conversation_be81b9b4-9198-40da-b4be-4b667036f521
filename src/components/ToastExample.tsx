import React from 'react';
import { showToast } from '../utils/toast';

/**
 * DaisyUI Toast 示例组件
 * 展示如何使用自定义的 toast 函数
 */
export const ToastExample: React.FC = () => {
    const handleSuccessToast = () => {
        showToast.success('操作成功！这是一个成功提示');
    };

    const handleErrorToast = () => {
        showToast.error('操作失败！这是一个错误提示');
    };

    const handleWarningToast = () => {
        showToast.warning('请注意！这是一个警告提示');
    };

    const handleInfoToast = () => {
        showToast.info('温馨提示！这是一个信息提示');
    };

    const handleDefaultToast = () => {
        showToast.default('这是一个默认提示');
    };

    const handleCustomToast = () => {
        showToast.custom(
            <div className="flex items-center">
                <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                <span>这是一个自定义 JSX 内容的提示</span>
            </div>,
            'info'
        );
    };

    const handlePromiseToast = () => {
        const promise = new Promise((resolve, reject) => {
            setTimeout(() => {
                if (Math.random() > 0.5) {
                    resolve('数据加载成功！');
                } else {
                    reject('数据加载失败！');
                }
            }, 2000);
        });

        showToast.promise(
            promise,
            {
                pending: '正在加载数据...',
                success: '数据加载成功！',
                error: '数据加载失败！'
            }
        );
    };

    return (
        <div className="card w-96 bg-base-100 shadow-xl">
            <div className="card-body">
                <h2 className="card-title">DaisyUI Toast 示例</h2>
                <p className="text-sm text-base-content/70 mb-4">
                    点击下面的按钮体验不同类型的 toast 提示
                </p>

                <div className="grid grid-cols-2 gap-2">
                    <button
                        className="btn btn-success btn-sm"
                        onClick={handleSuccessToast}
                    >
                        成功提示
                    </button>

                    <button
                        className="btn btn-error btn-sm"
                        onClick={handleErrorToast}
                    >
                        错误提示
                    </button>

                    <button
                        className="btn btn-warning btn-sm"
                        onClick={handleWarningToast}
                    >
                        警告提示
                    </button>

                    <button
                        className="btn btn-info btn-sm"
                        onClick={handleInfoToast}
                    >
                        信息提示
                    </button>

                    <button
                        className="btn btn-outline btn-sm"
                        onClick={handleDefaultToast}
                    >
                        默认提示
                    </button>

                    <button
                        className="btn btn-primary btn-sm"
                        onClick={handleCustomToast}
                    >
                        自定义提示
                    </button>
                </div>

                <button
                    className="btn btn-accent btn-sm mt-2"
                    onClick={handlePromiseToast}
                >
                    Promise 提示
                </button>
            </div>
        </div>
    );
};

export default ToastExample;

import { splitViralElements } from '@/utils/biz/common';
import { formatInteractionCount } from '@/utils';
import dayjs from 'dayjs';
import React, { useEffect } from 'react';
import { Drawer, Timeline } from 'react-daisyui';
import clsx from 'clsx';
import { ASSET_PROPERTIY, HANDLE_STATUS } from '@/cons';

interface AssetDetailDrawerProps {
    // 可以根据需要添加 props 类型定义
    visible: boolean;
    asset: any;
    onClose: () => void;
    topicNode?: React.ReactNode;
    onRegenerate?: () => void;
    onVideoPropertyClick?: (property: string) => void;
}

const AssetDetailDrawer: React.FC<AssetDetailDrawerProps> = ({ visible, asset = {}, onClose, topicNode, onRegenerate, onVideoPropertyClick }) => {
    // 监听 ESC 键按下事件
    useEffect(() => {
        const handleKeyDown = (event: KeyboardEvent) => {
            if (event.key === 'Escape' && visible) {
                onClose();
            }
        };

        // 只在 Drawer 可见时添加监听器
        if (visible) {
            document.addEventListener('keydown', handleKeyDown);
        }

        // 清理函数：移除事件监听器
        return () => {
            document.removeEventListener('keydown', handleKeyDown);
        };
    }, [visible, onClose]);

    return (
        <Drawer
            end
            open={visible}
            onClickOverlay={() => onClose()}
            side={(
                <>
                    <div className="bg-base-100 min-h-full w-full md:w-[768px] p-6 overflow-y-auto" role="dialog" aria-modal="true" aria-labelledby="drawer-title">
                        {/* 抽屉标题 */}
                        <div className="flex justify-between items-center mb-6">
                            <h3 className="text-xl font-bold">素材详情</h3>
                            <label htmlFor="drawer-detail" className="btn btn-sm btn-ghost" aria-label="关闭详情抽屉 (ESC)" onClick={onClose}>
                                <i className="fas fa-times"></i>
                            </label>
                        </div>

                        {/* 素材基本信息 */}
                        <div className="mb-6">
                            <div className="flex gap-4 mb-4">
                                <div className="w-24 h-40 rounded-lg bg-base-200 overflow-hidden">
                                    <a href={asset?.douyin_aweme?.AwemeURL} target="_blank" className="block relative w-full h-full">
                                        <div
                                            className="group w-full h-full"
                                        >
                                            <img
                                                src={asset?.douyin_aweme?.CoverURL}
                                                alt="视频封面"
                                                className="w-full h-full object-cover"
                                            />
                                            <div className="absolute top-0 left-0 w-full h-full flex justify-center items-center opacity-0 group-hover:opacity-100 transition-all">
                                                <div className="absolute top-0 left-0 w-full h-full bg-black opacity-50"></div>
                                                <i className="fa-solid fa-play text-white z-99"></i>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                                <div className="flex-1">
                                    <a href={asset?.douyin_aweme?.AwemeURL} target="_blank" className="link link-hover">
                                        <h4 className="text-base font-medium mb-2">{asset?.douyin_aweme?.Title}</h4>
                                    </a>

                                    <div className="flex items-center mb-3">
                                        <div className="avatar mr-2">
                                            <a href={`https://www.douyin.com/user/${asset?.douyin_aweme?.SecUID}`} target="_blank">
                                                <div className="w-6 h-6 rounded-full overflow-hidden">
                                                    <img src={asset?.douyin_aweme?.Avatar} alt="作者头像" />
                                                </div>
                                            </a>
                                        </div>
                                        <a href={`https://www.douyin.com/user/${asset?.douyin_aweme?.SecUID}`} target="_blank" className="hover:underline">
                                            <span className="text-sm">{asset?.douyin_aweme?.Nickname}</span>
                                        </a>
                                        {/* 互动数据 */}
                                        <div className="flex items-center gap-2 ml-3 text-sm opacity-60">
                                            <div className="flex items-center gap-1">
                                                <i className="fas fa-heart"></i>
                                                <span>{formatInteractionCount(asset?.douyin_aweme?.LikedCount)}</span>
                                            </div>
                                            <div className="flex items-center gap-1">
                                                <i className="fas fa-comment"></i>
                                                <span>{formatInteractionCount(asset?.douyin_aweme?.CommentCount)}</span>
                                            </div>
                                            <div className="flex items-center gap-1">
                                                <i className="fas fa-star"></i>
                                                <span>{formatInteractionCount(asset?.douyin_aweme?.CollectedCount)}</span>
                                            </div>
                                            <div className="flex items-center gap-1">
                                                <i className="fas fa-share"></i>
                                                <span>{formatInteractionCount(asset?.douyin_aweme?.ShareCount)}</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="text-sm opacity-60 mb-3"></div>

                                    {/* 视频标签、脚本类型和爆款元素 */}
                                    <div className="space-y-2 text-sm opacity-60">
                                        <div className="flex">
                                            <span className="w-20 flex-shrink-0">发布时间：</span>
                                            <div>
                                                {asset?.douyin_aweme?.CreateTime && dayjs(asset?.douyin_aweme?.CreateTime * 1000).format('YYYY-MM-DD HH:mm:ss')}
                                            </div>
                                        </div>
                                        <div className="flex">
                                            <span className="w-20 flex-shrink-0">视频标签：</span>
                                            <div>
                                                {splitViralElements(asset?.video_tags).map((tag, index) => (
                                                    <span key={index} className="bg-[#f3f4f6] color-[#4b5563] text-xs py-1 px-2 text-base-content rounded-full">
                                                        {tag}
                                                    </span>
                                                )) || '-'}
                                            </div>
                                        </div>
                                        <div className="flex">
                                            <span className="w-20 flex-shrink-0">脚本类型：</span>
                                            <span>{asset?.script_type || '-'}</span>
                                        </div>
                                        <div className="flex">
                                            <span className="w-20 flex-shrink-0">爆款元素：</span>
                                            <div>
                                                {splitViralElements(asset?.viral_elements).map((tag, index) => (
                                                    <span key={index} className="bg-[#f3f4f6] color-[#4b5563] text-xs py-1 px-2 text-base-content rounded-full">
                                                        {tag}
                                                    </span>
                                                )) || '-'}
                                            </div>
                                        </div>
                                        <div className="flex">
                                            <span className="w-20 flex-shrink-0">分析结果：</span>
                                            <span>
                                                {asset?.video_content ? (
                                                    <a className="link hover:underline" onClick={() => onVideoPropertyClick?.(ASSET_PROPERTIY.VIDEO_SUMMARY)}>查看素材分析结果</a>
                                                ) : '-'}
                                            </span>
                                        </div>
                                        <div className="flex">
                                            <span className="w-20 flex-shrink-0">视频原文：</span>
                                            <span>
                                                {asset?.analysis_results ? (
                                                    <a className="link hover:underline" onClick={() => onVideoPropertyClick?.(ASSET_PROPERTIY.ANALYSIS_RESULT)}>查看素材视频原文</a>
                                                ) : '-'}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* 雪碧图 */}
                        <div className="mb-6">
                            <div className="w-full bg-black">
                                {asset?.sprite_image && (
                                    <a href={`https://vod.qihaozhushou.com/${asset.sprite_image}~noop.image`} target="_blank" rel="noopener noreferrer">
                                        <img src={`https://vod.qihaozhushou.com/${asset.sprite_image}~noop.image`} alt="视频互动雪碧图" className="w-full" />
                                    </a>
                                )}
                            </div>
                        </div>

                        {/* 素材分析流程 */}
                        <div className="mb-6">
                            <Timeline color="neutral" responsive className="md:timeline-horizontal">
                                <Timeline.Item connect="end">
                                    <Timeline.Start box>完成视频结构理解和拆片</Timeline.Start>
                                    <Timeline.Middle />
                                </Timeline.Item>
                                <Timeline.Item connect="both">
                                    <Timeline.Middle />
                                    <Timeline.End>完成素材分析、脚本类型和爆款元素分析</Timeline.End>
                                </Timeline.Item>
                                <Timeline.Item connect="both">
                                    <Timeline.Start box>引用商业定位和分析结果</Timeline.Start>
                                    <Timeline.Middle />
                                </Timeline.Item>
                                <Timeline.Item connect="start">
                                    <Timeline.Middle />
                                    <Timeline.End>生成爆款选题</Timeline.End>
                                </Timeline.Item>
                            </Timeline>
                        </div>

                        {/* 为你生成的选题 */}
                        <div>
                            <h4 className="text-lg font-semibold mb-4">已为你生成 {asset?.topicList?.length} 条选题</h4>

                            <div>
                                <div className={clsx("mb-4", { hidden: ![HANDLE_STATUS.PENDING, HANDLE_STATUS.PROCESSING].includes(asset?.eino_execute_record?.handle_status), flex: [HANDLE_STATUS.PENDING, HANDLE_STATUS.PROCESSING].includes(asset?.eino_execute_record?.handle_status) })}>
                                    <div className="flex w-full items-center justify-center py-6 border border-base-300 rounded-lg bg-base-50">
                                        <span className="loading loading-spinner loading-md text-primary mr-3 !my-0"></span>
                                        <span className="text-base-content">正在生成更多选题，请稍后刷新页面查看。</span>
                                    </div>
                                </div>

                                <div className="overflow-x-auto">
                                    {topicNode}
                                </div>
                            </div>

                            <div className="justify-center items-center py-8 hidden">
                                <span className="loading loading-spinner loading-md"></span>
                                <span className="ml-2">加载中...</span>
                            </div>
                            <div className="text-center py-8 hidden">
                                <p className="text-base-content opacity-60">暂无关联选题</p>
                            </div>
                            <div className="text-center py-8 hidden">
                                <i className="fas fa-exclamation-triangle text-error mr-2"></i>
                                <span>加载失败，请稍后再试</span>
                            </div>
                        </div>

                        {/* 底部操作栏 */}
                        <div className="mt-8 flex justify-end">
                            <button
                                className="btn btn-neutral"
                                id="btn-generate-topics"
                                onClick={onRegenerate}
                            >
                                <i className="fas fa-magic mr-2"></i>再次生成
                            </button>
                        </div>
                    </div>
                </>
            )}
        />
    );
};

export default AssetDetailDrawer;

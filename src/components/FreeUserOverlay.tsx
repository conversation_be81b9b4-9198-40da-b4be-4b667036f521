import React from 'react';
import { useAuthStore } from '@/models/auth';
import { ROLES } from '@/cons';
import { useLocation, Navigate } from 'react-router-dom';
import { ROUTES } from '../routes/constants';

// QR code image path - updated to use the provided URL
const QR_CODE_URL = 'https://cdn.qihaozhushou.com/website/20250423-main-37ecaf3a/chenziyi_work_weixin_qrcode.jpeg';

const FreeUserOverlay: React.FC = () => {
  const { state } = useAuthStore();
  const location = useLocation();
  const isFree = state.userInfo?.role === ROLES.FREE;
  const isDashboard = location.pathname === ROUTES.DASHBOARD;
  
  // If user is not FREE, don't show overlay
  if (!isFree) {
    return null;
  }
  
  // If not on dashboard, redirect to dashboard
  if (!isDashboard) {
    return <Navigate to={ROUTES.DASHBOARD} replace />;
  }

  return (
    <div 
      className="fixed inset-0 flex items-center justify-center z-[9999]"
      style={{ backgroundColor: 'rgba(0, 0, 0, 0.85)' }}
    >
      <div className="bg-white rounded-2xl shadow-xl w-[500px] text-center px-10 py-8">
        <h3 className="text-xl font-bold mb-6">请添加我们的商务经理，解锁体验权限</h3>

        <div className="mb-4 flex justify-center">
          <div className="qr-code-container">
            <img 
              src={QR_CODE_URL} 
              alt="商务经理微信二维码" 
              className="w-64 h-64 mx-auto"
            />
          </div>
        </div>
        <p className="text-gray-700 text-sm mt-2">备注"解锁"</p>
      </div>
    </div>
  );
};

export default FreeUserOverlay; 
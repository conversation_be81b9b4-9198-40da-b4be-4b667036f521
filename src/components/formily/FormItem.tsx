import { takeMessage } from "@/formily/utils"
import { Field } from "@formily/core"
import { observer, useField } from "@formily/react"
import clsx from "clsx"


export const FormItem = observer((props: any) => {
    const field = useField() as Field
    const messages = takeMessage(field) || []
    const showMessage = messages.join(', ')

    return (
        <div {...props}>
            {props.children}
            <p className={clsx("validator-hint text-error", field.validateStatus === 'error' ? '' : 'hidden')}>
                {showMessage}
            </p>
        </div>
    )
})
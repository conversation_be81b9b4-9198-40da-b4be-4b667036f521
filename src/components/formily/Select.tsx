import { Field } from "@formily/core"
import { observer, useField } from "@formily/react"
import clsx from "clsx"
import { Select as OriginSelect } from "react-daisyui"


export const Select = observer((props: any) => {
    const field = useField() as Field
    return (
        <OriginSelect {...props} className={clsx('validator-user', props.className)} color={field.validateStatus}>
            {field.dataSource.map(item => (
                <OriginSelect.Option key={item.value} value={item.value}>{item.label}</OriginSelect.Option>
            ))}
        </OriginSelect>
    )
})
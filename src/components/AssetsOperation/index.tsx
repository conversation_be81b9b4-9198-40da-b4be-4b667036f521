import { createForm } from "@formily/core";
import { Field, FormProvider } from "@formily/react";
import { useCallback, useMemo, useRef, useState } from "react";
import { Button, Modal } from "react-daisyui"
import { FormItem } from "../formily/FormItem";
import { Textarea } from "../formily/Textarea";
import { useAssetsStore } from "@/models/assets";
import { useAuthStore } from "@/models/auth";
import { ROLES } from "@/cons";
import api from "@/api";
import { toast } from "react-toastify";

const form = createForm({
    initialValues: {
        douyin_url: '',
    }
})

export const AssetsOperation = ({ onRefresh, showDouyinSync = false }: { onRefresh?: () => void, showDouyinSync?: boolean }) => {
    const store = useAssetsStore()
    const { state } = useAuthStore()
    const ref = useRef<HTMLDialogElement>(null);
    const syncDouyinRef = useRef<HTMLDialogElement>(null);
    const commercialPosRef = useRef<HTMLDialogElement>(null);
    const cookieRef = useRef<HTMLDialogElement>(null);
    const isRestricted = useMemo(() =>
        state.userInfo?.role === ROLES.FREE || state.userInfo?.role === ROLES.LIMITED,
        [state.userInfo?.role]
    );
    const [loading, setLoading] = useState(false);

    const handleShow = useCallback(() => {
        if (!state.userInfo?.commercial_pos) {
            commercialPosRef.current?.showModal()
            return
        }

        ref.current?.showModal();
    }, [ref]);
    const handleCollectionSyncShow = useCallback(() => {
        if (!state.userInfo?.commercial_pos) {
            commercialPosRef.current?.showModal()
            return
        }
        if (!state.userInfo?.douyin_cookie) {
            cookieRef.current?.showModal()
            return
        }

        syncDouyinRef.current?.showModal()
    }, [syncDouyinRef]);
    const handleClose = useCallback(() => {
        ref.current?.close();
    }, [ref]);

    const handleSubmit = useCallback(async () => {
        await form.validate()

        const values = form.values
        setLoading(true)
        try {
            await store.actions.addAsset(values)
            handleClose()
            form.reset()
            onRefresh?.();
        } catch (error) {
            const { data } = (error as any)
            if (data.code === 405003) {
                cookieRef.current?.showModal()
            } else if (data.code === 405002) {
                commercialPosRef.current?.showModal()
            }
        } finally {
            syncDouyinRef.current?.close()
            setLoading(false)
        }
    }, [form])

    const handleSyncDouyinCollection = async () => {
        try {
            setLoading(true)
            await api.post('/client/user/douyin/syncCollects')
            // store.actions.startAssetsStatusStream();
            toast('同步成功')
            onRefresh?.();
        } catch (error) {
            const { data } = (error as any)
            if (data.code === 405003) {
                cookieRef.current?.showModal()
            } else if (data.code === 405002) {
                commercialPosRef.current?.showModal()
            }
        } finally {
            setLoading(false)
            syncDouyinRef.current?.close()
        }
    }

    return (
        <>
            <Button disabled={isRestricted} className="bg-black text-white hover:bg-gray-800" onClick={handleShow}>
                <i className="fas fa-upload mr-2"></i>手动添加素材
            </Button>

            {showDouyinSync && <Button className="btn-outline" onClick={handleCollectionSyncShow}>
                <i className="fas fa-sync-alt mr-2"></i>同步抖音收藏夹
            </Button>}


            <Modal ref={ref} containerClasses="modal-bottom sm:modal-middle">
                <FormProvider form={form}>
                    <Modal.Header className="text-lg font-bold mb-4">手动添加素材</Modal.Header>
                    <Modal.Body>
                        <div className="mb-4">
                            <div className="text-base font-medium mb-2">抖音链接</div>
                            <Field
                                name="douyin_url"
                                required
                                decorator={[FormItem]}
                                component={[Textarea, { bordered: true, className: 'w-full h-24', placeholder: '抖音视频的分享链接' }]}
                            />
                        </div>
                    </Modal.Body>
                    <Modal.Actions>
                        <div className="flex gap-2">
                            <Button loading={loading} disabled={loading} onClick={handleClose}>取消</Button>
                            <Button loading={loading} disabled={loading} className="bg-black text-white hover:bg-gray-800" type="submit" onClick={handleSubmit}>确定</Button>
                        </div>
                    </Modal.Actions>
                </FormProvider>
            </Modal>


            <Modal ref={syncDouyinRef} containerClasses="modal-bottom sm:modal-middle">
                <Modal.Body>
                    <h3 className="text-lg font-bold mb-4">同步抖音收藏夹</h3>
                    <div className="mb-4">
                        <p className="mb-2">将同步你的抖音收藏夹中，包含"素材"字样的收藏夹</p>
                    </div>
                    <Modal.Actions className="flex gap-2">
                        <Button disabled={loading} loading={loading} onClick={() => syncDouyinRef.current?.close()}>取消</Button>
                        <Button disabled={loading} loading={loading} className="bg-black text-white hover:bg-gray-800" onClick={() => handleSyncDouyinCollection()}>开始同步</Button>
                    </Modal.Actions>
                </Modal.Body>
            </Modal>


            <Modal backdrop ref={commercialPosRef} containerClasses="modal-bottom sm:modal-middle">
                <Modal.Header className="font-bold">未配置商业定位</Modal.Header>
                <Modal.Body>
                    请先在「工作台→基础配置」中配置商业定位。
                </Modal.Body>
                <Modal.Actions>
                    <form method="dialog">
                        <Button>关闭</Button>
                    </form>
                </Modal.Actions>
            </Modal>


            <Modal backdrop ref={cookieRef} containerClasses="modal-bottom sm:modal-middle">
                <Modal.Header className="font-bold">未配置抖音 Cookie</Modal.Header>
                <Modal.Body>
                    请先在「工作台→基础配置」中配置抖音 Cookie。<a target="_blank" href={import.meta.env.VITE_COOKIE_DOC_URL} className="btn btn-link btn-m px-1">查看教程 →</a>
                </Modal.Body>
                <Modal.Actions>
                    <form method="dialog">
                        <Button>关闭</Button>
                    </form>
                </Modal.Actions>
            </Modal>
        </>
    )
}
import React from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { ROUTES } from '../routes/constants';
import { useAuthStore } from '@/models/auth';
import { ROLES } from '@/cons';
import QueryString from 'qs';

interface ProtectedRouteProps {
  children?: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { state } = useAuthStore();
  const location = useLocation();

  if (!state.isLoggedIn) {
    const queryParamsText = QueryString.stringify({
      source_path: location.pathname + location.search
    })
    return <Navigate to={`${ROUTES.LOGIN}?${queryParamsText}`} replace />;
  }

  // If FREE user and not on dashboard, redirect to dashboard
  if (state.userInfo?.role === ROLES.FREE && location.pathname !== ROUTES.DASHBOARD) {
    return <Navigate to={ROUTES.DASHBOARD} replace />;
  }

  // If LIMITED user and not on tools page, redirect to tools page
  if (state.userInfo?.role === ROLES.LIMITED && (location.pathname !== ROUTES.TOOLS && location.pathname !== ROUTES.PRICE)) {
    return <Navigate to={ROUTES.TOOLS} replace />;
  }

  return children ? <>{children}</> : <Outlet />;
};

export default ProtectedRoute; 
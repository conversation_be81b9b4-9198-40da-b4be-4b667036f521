import React, { useState, useEffect } from 'react';
import { Outlet, useMatches } from 'react-router-dom';
import { Header } from './Header';
import { SideMenu } from './SideMenu';
import { useAuthStore } from '@/models/auth';
import { useDocumentStore } from '@/models/document';
import { useUserStore } from '@/models/user';
import { useInboxStore } from '@/models/inbox';

import FreeUserOverlay from './FreeUserOverlay';

const Layout: React.FC = () => {
  const { actions } = useAuthStore();
  const { actions: userActions } = useUserStore();
  const { actions: inboxActions } = useInboxStore();
  const { reducers } = useDocumentStore();
  const [visible, setVisible] = useState(false);
  const matches = useMatches();

  useEffect(() => {
    if (matches.length > 0) {
      const match = matches[matches.length - 1]
      reducers.setDocumentTitle((match?.handle as any)?.title)
    }
  }, [matches])

  const onSwap = () => {
    setVisible(true);
  }

  useEffect(() => {
    actions.getUserInfo();
    userActions.getDataStats();
    inboxActions.fetchVideoRelatedStats();
  }, []);

  return (
    <div className="min-h-screen flex flex-col">
      <Header onSwap={onSwap} swapActive={visible} />
      <div className="flex flex-1 w-full h-full">
        <SideMenu visible={visible} closeDrawer={() => setVisible(!visible)} />
        <main className="ransition-all duration-300 min-w-1 w-full">
          <div className="p-4 lg:p-6 max-h-[100vh] overflow-y-auto">
            <Outlet />
          </div>
        </main>
      </div>
      <FreeUserOverlay />
    </div>
  );
};

export default Layout; 
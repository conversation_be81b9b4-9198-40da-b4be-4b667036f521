import { useDocumentStore } from '@/models/document';
import React from 'react';
import { Button, Navbar } from 'react-daisyui';

interface HeaderProps {
  onSwap?: (e: any) => void;
  swapActive?: boolean;
}

export const Header: React.FC<HeaderProps> = ({ onSwap }) => {
    const { state } = useDocumentStore();
  
  return (
    <Navbar className="bg-base-100 shadow-sm lg:hidden">
      <div className="flex-none">
        <Button color="ghost" className="btn-square" onClick={onSwap}>
          <i className="fas fa-bars"></i>
        </Button>
      </div>
      <div className="flex-1">
        <span className="text-xl font-bold">{state.document.title}</span>
      </div>
    </Navbar>
  );
}; 
import { APPROVAL_STATUS } from '@/cons';
import { showTime } from '@/utils';
import { splitViralElements } from '@/utils/biz/common';
import clsx from 'clsx';
import { Table } from 'react-daisyui';

type Props = {
    data: any[];
    onOperation?: (topic: any, index: number, targetStatus: APPROVAL_STATUS) => void;
    embeded?: boolean;
};

export default function TopicTable({ data = [], onOperation, embeded = false }: Props) {
    return (
        <Table className={clsx('w-full table-fixed')} zebra>
            <thead>
                <tr>
                    <th className="w-4">#</th>
                    <th className="w-48">选题标题</th>
                    <th className={clsx("w-24", { 'hidden md:table-cell': embeded })}>脚本类型</th>
                    <th className={clsx("w-40", { 'hidden': embeded })}>爆款元素</th>
                    <th className={clsx("w-24", { 'hidden md:table-cell': embeded })}>添加时间</th>
                    <th className="w-20">操作</th>
                </tr>
            </thead>

            <Table.Body>
                {data.map((topicItem, index) => (
                    <tr key={topicItem.uuid}>
                        <td className="truncate">
                            {index + 1}
                        </td>
                        <td title={topicItem.topic_title}>
                            {topicItem.topic_title}
                        </td>
                        <td className={clsx({ 'hidden md:table-cell': embeded })}>{topicItem.script_type}</td>
                        <td className={clsx({ 'hidden': embeded })}>
                            {splitViralElements(topicItem.viral_elements).map((ele, index) => (
                                <span key={index} className="badge badge-sm bg-gray-200 text-gray-700 border-none mr-1">{ele}</span>
                            ))}
                        </td>
                        <td className={clsx({ 'hidden md:table-cell': embeded })}>{showTime(topicItem.createTime)}</td>
                        <td>
                            {topicItem.approval_status === APPROVAL_STATUS.APPROVED ? (
                                <button className="btn btn-outline btn-xs btn-success btn-dash w-20" onClick={() => onOperation?.(topicItem, index, APPROVAL_STATUS.PENDING)}>已确认</button>
                            ) : (
                                <button className="btn btn-outline btn-xs w-20" onClick={() => onOperation?.(topicItem, index, APPROVAL_STATUS.APPROVED)}>确认选题</button>
                            )}
                        </td>
                    </tr>
                ))}
            </Table.Body>
        </Table>
    )
}
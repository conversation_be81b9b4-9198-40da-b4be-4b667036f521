import clsx from "clsx";
import { useMemo } from "react";
import { Button, Pagination } from "react-daisyui";


export const CustomPagination = ({
    currentPage,
    pageSize,
    total,
    onChange,
    disabled,
}: {
    currentPage: number;
    pageSize: number;
    total: number;
    disabled: boolean;
    onChange: (page: number) => void;
}) => {
    const totalPages = Math.ceil(total / pageSize);
    const generatePageList = (count: number, startPage: number) => new Array(count).fill(0).map((_, index) => startPage + index + 1)
    const pageNumberList = useMemo(() => {
        const MAX_COUNT = 5
        const startPage = 0
        const ACTUAL_COUNT = Math.min(totalPages, MAX_COUNT)

        if (totalPages <= MAX_COUNT) return generatePageList(ACTUAL_COUNT, startPage)
        else if (totalPages - currentPage < MAX_COUNT) return generatePageList(ACTUAL_COUNT, Math.max(totalPages - MAX_COUNT, startPage))
        else return generatePageList(ACTUAL_COUNT, Math.max(currentPage - 3, startPage))
    }, [currentPage, totalPages])

    console.log('pageNumberList: ', pageNumberList)
    
    return (
        <Pagination>
            <Button className="join-item" disabled={currentPage === 1 || disabled} onClick={() => onChange(1)}>«</Button>
            <Button className="join-item" disabled={currentPage === 1 || disabled} onClick={() => onChange(currentPage - 1)}>‹</Button>
            {pageNumberList?.map(page => <Button key={page} disabled={disabled} className={clsx("join-item", currentPage === page && "btn-active")} onClick={() => onChange(page)}>{page}</Button>)}
            <Button className="join-item" disabled={currentPage === totalPages || disabled} onClick={() => onChange(currentPage + 1)}>›</Button>
            <Button className="join-item" disabled={currentPage === totalPages || disabled} onClick={() => onChange(totalPages)}>»</Button>
        </Pagination>
    );
};
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Modal } from 'react-daisyui';
import BusinessIcon from '@/assets/svg/business-position.svg?react';
import DouyinIcon from '@/assets/svg/douyin.svg?react';
import CookieIcon from '@/assets/svg/cookie.svg?react';
import IdentityIcon from '@/assets/svg/identity.svg?react';
import { useAuthStore } from '@/models/auth';
import clsx from 'clsx';
import { AssetsOperation } from '@/components/AssetsOperation';
import packageJson from '../../package.json'
import { createForm } from '@formily/core';
import { FormProvider } from '@formily/react';
import { observable } from '@formily/reactive';
import { SchemaField } from '@/formily/field';
import { useUserStore } from '@/models/user';


interface DashboardCard {
  title: string;
  key: string;
  icon: React.ReactNode;
  iconClass: string;
  placeholder: string;
}

interface UserInfo {
  commercial_pos?: string;
  douyin_id?: string;
  douyin_cookie?: string;
  identity?: string;
}

const dashboardCards: DashboardCard[] = [
  {
    title: '商业定位', 
    key: 'commercial_pos', 
    icon: <BusinessIcon />, 
    iconClass: 'fas fa-bullseye',
    placeholder: '设置您的商业目标、内容定位和人设'
  },
  { 
    title: '抖音号', 
    key: 'douyin_id', 
    icon: <DouyinIcon />, 
    iconClass: 'fas fa-music',
    placeholder: '关联您的抖音账号'
  },
  { 
    title: '抖音 Cookie', 
    key: 'douyin_cookie', 
    icon: <CookieIcon />, 
    iconClass: 'fas fa-cookie',
    placeholder: '配置后可自动化抓取你的抖音收藏夹'
  },
  { 
    title: '身份', 
    key: 'identity', 
    icon: <IdentityIcon />, 
    iconClass: 'fas fa-user-gear',
    placeholder: '设置你的身份'
  },
];

const identityList = [
  {
    label: '创作者',
    value: 'CREATOR'
  },
  {
    label: '机构/代运营',
    value: 'AGENCY'
  }
]

// Fix type for identityLabelMap
const identityLabelMap: Record<string, string> = identityList.reduce((acc: Record<string, string>, current) => {
  acc[current.value] = current.label
  return acc
}, {})

const DashboardMap = dashboardCards.reduce((acc, card) => {
  acc[card.key] = card;
  return acc;
}, {} as Record<string, DashboardCard>);


const form = createForm({
  initialValues: {
    commercial_pos: '',
    douyin_id: '',
    douyin_cookie: '',
    identity: 'CREATOR'
  },
})

const Dashboard: React.FC = observable(() => {
  const ref = useRef<HTMLDialogElement>(null);
  const { state: userState, actions: userActions } = useUserStore();
  const [key, setKey] = useState('');
  const formStatus = useMemo(() => observable({
    currentKey: ''
  }), [])

  const [loading, setLoading] = useState(true);
  const { state, actions } = useAuthStore();
  const handleChangeClick = useCallback((key: keyof UserInfo) => {
    formStatus.currentKey = key;
    setKey(key)
    form.setValues(state.userInfo)
    ref.current?.showModal();
  }, [ref, state.userInfo])
  
  const fetchStats = useCallback(async () => {
    await userActions.getDataStats();
    setLoading(false)
  }, [])

  useEffect(() => {
    fetchStats()
  }, [actions]);

  const handleUpdate = useCallback(async (e: React.MouseEvent<HTMLButtonElement>) => {
    await form.validate()
    const values = form.values

    e.preventDefault();
    ref.current?.close();
    await actions.wrapperUpdate(formStatus.currentKey, (values as any)[formStatus.currentKey]);
  }, [actions]);

  return (
    <div>
      <header className="mb-6">
        <h1 className="text-2xl font-bold">欢迎回来，{state.userInfo?.wx_nickname}</h1>
        <p className="text-base-content opacity-60">每一个爆款背后，都是无数次坚持和日拱一卒</p>
      </header>

      <div className="flex flex-col md:flex-row items-start md:items-center gap-2 mb-6">
        <AssetsOperation />
        <p className="text-sm opacity-70">手动添加素材后，将自动生成 5 个爆款选题。</p>
      </div>

      <h2 className="text-xl font-bold mb-4">基础配置</h2>
      <div className={clsx("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8", { '!hidden': !loading })}>
        {dashboardCards.map(
          (card) => (
            <Card key={card.key} side="lg" className={clsx("bg-base-100 shadow-md")}>
              <Card.Body className="w-full">
                <div className="flex items-center gap-2 mb-4">
                  <div className="skeleton w-8 h-8 rounded-full"></div>
                  <div className="skeleton h-6 w-1/3"></div>
                </div>

                <div className="space-y-2">
                  <div className="skeleton h-4 w-full"></div>
                  <div className="skeleton h-4 w-5/6"></div>
                  <div className="skeleton h-4 w-4/6"></div>
                </div>

                <div className="mt-4">
                  <div className="skeleton h-6 w-16 rounded-full"></div>
                </div>

                <div className="card-actions justify-end mt-4">
                  <div className="skeleton h-8 w-16 rounded-lg"></div>
                </div>
              </Card.Body>
            </Card>
          )
        )}
      </div>
      <div className={clsx("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8", { '!hidden': loading })}>
        {dashboardCards.map((card) => (
          <Card side="lg" className="bg-base-100 shadow-md hover:shadow-lg transition-shadow" key={card.key}>
            <Card.Body className="w-full">
              <Card.Title tag="h3" className="text-lg">
                <i className={clsx('text-gray-600', 'mr-2', card.iconClass)}></i>{card.title}
              </Card.Title>

              {state.userInfo?.[card.key as keyof UserInfo] ? (
                <>
                  {card.key !== 'identity' && <p className="text-sm opacity-60 line-clamp-4">{state.userInfo?.[card.key as keyof UserInfo]}</p>}
                  {card.key === 'identity' && <p className="text-sm opacity-60">{state.userInfo?.identity ? identityLabelMap[state.userInfo.identity] : ''}</p>}
                  <div className="mt-4">
                    <Badge>已设置</Badge>
                  </div>
                </>
              ) : (
                <>
                  <p className="text-sm opacity-60">{card.placeholder}</p>
                  <div className="mt-4">
                    <Badge>未设置</Badge>
                  </div>
                </>
              )}
              
              <Card.Actions className="justify-end mt-4">
                <Button className="btn-outline" size="sm" onClick={() => handleChangeClick(card.key as keyof UserInfo)} loading={state.isLoading} disabled={state.isLoading}>
                  {state.userInfo?.[card.key as keyof UserInfo] ? '修改' : '去设置'}
                </Button>
              </Card.Actions>
            </Card.Body>
          </Card>
        ))}
      </div>

      <h2 className="text-xl font-bold mb-4">数据概览</h2>
      <div className={clsx("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6", { '!hidden': !loading })}>
        {Array(5).fill(0).map((_, index) => (
          <div key={index} className={clsx("stat bg-base-100 shadow rounded-box")}>
            <div className="stat-figure text-base-content opacity-60">
              <div className="skeleton w-10 h-10 rounded-full"></div>
            </div>
            <div className="stat-title opacity-60">
              <div className="skeleton h-4 w-24"></div>
            </div>
            <div className="stat-value">
              <div className="skeleton h-8 w-16"></div>
            </div>
          </div>
        ))}
      </div>
      <div className={clsx("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6", { '!hidden': loading })}>
        <div className="stat bg-base-100 shadow rounded-box">
          <div className="stat-figure text-base-content opacity-60">
            <i className="fas fa-photo-film text-3xl"></i>
          </div>
          <div className="stat-title opacity-60">素材总数</div>
          <div className="stat-value">{userState.dataStats.asset_count}</div>
        </div>

        <div className="stat bg-base-100 shadow rounded-box">
          <div className="stat-figure text-base-content opacity-60">
            <i className="fas fa-lightbulb text-3xl"></i>
          </div>
          <div className="stat-title opacity-60">选题总数</div>
          <div className="stat-value">{userState.dataStats.topic_count}</div>
        </div>

        <div className="stat bg-base-100 shadow rounded-box">
          <div className="stat-figure text-base-content opacity-60">
            <i className="fas fa-wrench text-3xl"></i>
          </div>
          <div className="stat-title opacity-60">公共工具数</div>
          <div className="stat-value">{userState.dataStats.tool_count}</div>
        </div>

        <div className="stat bg-base-100 shadow rounded-box">
          <div className="stat-figure text-base-content opacity-60">
            <i className="fas fa-user-lock text-3xl"></i>
          </div>
          <div className="stat-title opacity-60">私有工具数（即将上线）</div>
          <div className="stat-value">0</div>
        </div>
      </div>

      <h2 className="text-xl font-bold mb-4">版本更新</h2>
      <div className={clsx("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8", { '!hidden': !loading })}>
        <div className="stat bg-base-100 shadow rounded-box p-6">
          <div className="flex justify-between items-center mb-6">
            <div>
              <div className="flex items-center gap-2">
                <div className="skeleton w-6 h-6 rounded-full"></div>
                <div className="skeleton h-5 w-24"></div>
              </div>
              <div className="skeleton h-3 w-16 mt-2"></div>
            </div>
            <div className="skeleton h-6 w-12"></div>
          </div>
          <div className="flex justify-end">
            <div className="skeleton h-8 w-20 rounded-lg"></div>
          </div>
        </div>
      </div>
      <div className={clsx("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8", { '!hidden': loading })}>
        <div className="stat bg-base-100 shadow rounded-box p-6">
          <div className="flex justify-between items-center mb-6">
            <div>
              <Card.Title tag="h3" className="text-lg mb-0 font-bold">
                <i className="text-gray-600 mr-2 fas fa-code-branch"></i>当前版本
              </Card.Title>
              <div className="text-gray-500 text-sm">{import.meta.env.VITE_APP_APPLICATION_VERSION}</div>
            </div>
            <div className="text-lg font-bold">{packageJson.version}</div>
          </div>
          <div className="flex justify-end">
            <Button 
              className="btn-outline" 
              size="sm"
              onClick={() => window.open('https://tech-done.feishu.cn/docx/Nn0TdAfB0o81azx2bVBctP5An1c?from=from_copylink', '_blank')}
            >
              更新说明
            </Button>
          </div>
        </div>
      </div>

      <Modal containerClasses="modal-bottom sm:modal-middle" ref={ref}>
        <Modal.Header className="font-bold">{DashboardMap[key]?.title}</Modal.Header>
        <FormProvider form={form}>
          <Modal.Body>
            <div className="flex w-full component-preview py-4 items-center justify-center gap-2 font-sans">
              <SchemaField scope={{ formStatus }}>
                <SchemaField.String
                  name="commercial_pos"
                  required
                  x-visible="{{formStatus.currentKey === 'commercial_pos'}}"
                  x-decorator="FormItem"
                  x-decorator-props={{
                    className: 'form-control w-full flex flex-col'
                  }}
                  x-component="Textarea"
                  x-component-props={{
                    bordered: true, className: 'w-full h-24', placeholder: '请输入商业定位'
                  }}
                />
                <SchemaField.String
                  name="douyin_id"
                  required
                  x-visible="{{formStatus.currentKey === 'douyin_id'}}"
                  x-decorator="FormItem"
                  x-decorator-props={{
                    className: 'form-control w-full flex flex-col'
                  }}
                  x-component="Textarea"
                  x-component-props={{
                    bordered: true, className: 'w-full h-24', placeholder: '请输入抖音号'
                  }}
                />
                <SchemaField.String
                  name="douyin_cookie"
                  required
                  x-visible="{{formStatus.currentKey === 'douyin_cookie'}}"
                  x-decorator="FormItem"
                  x-decorator-props={{
                    className: 'form-control w-full flex flex-col'
                  }}
                  x-component="Textarea"
                  x-component-props={{
                    bordered: true, className: 'w-full h-24', placeholder: '请输入抖音 Cookie'
                  }}
                />
                <SchemaField.String
                  name="identity"
                  required
                  x-visible="{{formStatus.currentKey === 'identity'}}"
                  x-decorator="FormItem"
                  default="CREATOR"
                  x-decorator-props={{
                    className: 'form-control w-full flex flex-col'
                  }}
                  x-component="Select"
                  x-component-props={{}}
                  enum={identityList}
                />
              </SchemaField>
            </div>
          </Modal.Body>
          <Modal.Actions>
            <div className="flex gap-2">
              <Button onClick={() => ref.current?.close()}>取消</Button>
              <Button className="bg-black text-white" onClick={handleUpdate}>确定</Button>
            </div>
          </Modal.Actions>
        </FormProvider>
      </Modal>
    </div>
  );
});

export default Dashboard; 
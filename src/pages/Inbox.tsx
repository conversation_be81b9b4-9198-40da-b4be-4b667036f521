import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>, Button, Card, Modal } from 'react-daisyui';
import { toast } from 'react-toastify';
import { apiClient } from '@/api/generated/client';
import type { components } from '@/api/generated/types';
import { HANDLE_STATUS } from '@/cons';
import { useInboxStore } from '@/models/inbox';
import { formatDateTime } from '@/utils';

// API 响应类型定义
type VideoRelatedRecord = components['schemas']['VideoRelatedRecord'];

// 直接使用 API 类型，不创建自定义接口
type VideoItem = VideoRelatedRecord;

// 分页信息接口
interface PaginationInfo {
  page: number;
  page_size: number;
  total: number;
}



const Inbox: React.FC = () => {
  // 解析统计数字（处理 DouyinAweme 的字符串格式数字）
  const parseStatNumber = (value: string | undefined): number => {
    if (!value) return 0;

    // 如果已经是格式化的字符串（如 "1.2w", "65954"）
    if (typeof value === 'string') {
      // 处理万单位
      if (value.includes('w') || value.includes('万')) {
        const numStr = value.replace(/[w万]/g, '');
        const num = parseFloat(numStr);
        return isNaN(num) ? 0 : Math.round(num * 10000);
      }

      // 处理普通数字字符串
      const num = parseInt(value);
      return isNaN(num) ? 0 : num;
    }

    return 0;
  };

  const [activeFilter, setActiveFilter] = useState<'all' | 'favorite' | 'account' | 'keyword'>('all');
  const [sortBy, setSortBy] = useState<'addedTime' | 'publishTime'>('addedTime');
  const [selectedVideo, setSelectedVideo] = useState<VideoItem | null>(null);
  // 完全使用真实 API 数据，不再使用模拟数据
  const importModalRef = useRef<HTMLDialogElement>(null);

  // 新增状态：API 数据管理
  const [relatedVideos, setRelatedVideos] = useState<VideoRelatedRecord[]>([]);
  const [isLoadingRelated, setIsLoadingRelated] = useState(false);
  const [relatedError, setRelatedError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    page_size: 20,
    total: 0
  });

  // 使用全局统计状态 - 分别获取每个状态避免对象重新创建
  const syncStats = useInboxStore((state) => state.state.syncStats);
  const videoRelatedStats = useInboxStore((state) => state.state.videoRelatedStats);
  const isLoadingStats = useInboxStore((state) => state.state.isLoadingStats);
  const isLoadingVideoStats = useInboxStore((state) => state.state.isLoadingVideoStats);
  const statsError = useInboxStore((state) => state.state.statsError);
  const videoStatsError = useInboxStore((state) => state.state.videoStatsError);

  // 单独获取 actions，避免在 useEffect 依赖中使用
  const fetchVideoRelatedStats = useInboxStore((state) => state.actions.fetchVideoRelatedStats);





  // 获取相关视频列表
  const fetchRelatedVideoList = async (
    page: number = 1,
    pageSize: number = 20,
    sourceType?: 'video' | 'author' | 'collect',
    sortBy?: 'create_time' | 'publish_time',
    sortOrder: 'asc' | 'desc' = 'desc'
  ) => {
    try {
      setIsLoadingRelated(true);
      setRelatedError(null);

      const { data, error } = await apiClient.GET('/client/user/douyin/getVideoRelatedList', {
        params: {
          query: {
            source_type: sourceType,
            sort_by: sortBy,
            sort_order: sortOrder,
            page,
            page_size: pageSize
          }
        }
      });

      if (error) {
        throw new Error(error.msg || '获取相关视频列表失败');
      }

      if (data?.code === 0) {
        setRelatedVideos(data.data?.list || []);
        setPagination({
          page: data.data?.page || 1,
          page_size: data.data?.page_size || 20,
          total: data.data?.total || 0
        });
        setRelatedError(null);
      } else {
        throw new Error(data?.message || '获取相关视频列表失败');
      }
    } catch (error: any) {
      console.error('获取相关视频列表失败:', error);
      setRelatedError(error.message || '获取相关视频列表失败');
      setRelatedVideos([]);
    } finally {
      setIsLoadingRelated(false);
    }
  };

  // 组件挂载时获取数据
  useEffect(() => {
    fetchRelatedVideoList();
    // 进入页面时刷新统计数据（不强制刷新，会检查缓存）
    // 直接调用 store 的 actions，避免依赖项问题
    useInboxStore.getState().actions.fetchVideoRelatedStats(false);
  }, []);

  // 直接使用 API 数据，不进行转换
  const combinedVideoData = relatedVideos;

  // 统计 douyin_aweme 数据使用情况
  const awemeDataStats = {
    total: combinedVideoData.length,
    withAwemeData: combinedVideoData.filter(v => v.douyin_aweme).length,
    withThumbnail: combinedVideoData.filter(v => v.douyin_aweme?.CoverURL).length,
    withStats: combinedVideoData.filter(v => v.douyin_aweme?.LikedCount).length,
  };

  // 在开发环境下输出统计信息
  React.useEffect(() => {
    if (combinedVideoData.length > 0) {
      console.log('📊 Douyin Aweme 数据使用统计:', awemeDataStats);
    }
  }, [combinedVideoData.length]);

  // 统计数据 - 基于现有四个卡片，优先使用 API 数据
  const stats = {
    // 收件箱：使用视频相关统计的总数，备用同步统计，最后备用本地计算
    total: videoRelatedStats?.total || syncStats?.total_synced_videos || combinedVideoData.length,
    // 收藏夹：使用视频相关统计的收藏夹数据，备用本地计算
    favorite: videoRelatedStats?.source_type_stats?.collect || combinedVideoData.filter(v => v.source_type === 'collect').length,
    // 监控账号：使用视频相关统计的作者数据，备用本地计算
    account: videoRelatedStats?.source_type_stats?.author || combinedVideoData.filter(v => v.source_type === 'author').length,
    // 监控关键词：使用视频相关统计的关键词数据，备用本地计算
    keyword: videoRelatedStats?.source_type_stats?.video || combinedVideoData.filter(v => v.source_type === 'video').length,
  };

  // 获取卡片描述信息
  const getCardDescription = (cardType: 'total' | 'favorite' | 'account' | 'keyword') => {
    // 处理加载状态
    if ((isLoadingStats || isLoadingVideoStats) && cardType === 'total') {
      return '正在加载...';
    }

    if ((isLoadingVideoStats) && (cardType === 'favorite' || cardType === 'account' || cardType === 'keyword')) {
      return '正在加载...';
    }

    // 处理错误状态
    if ((statsError || videoStatsError) && cardType === 'total') {
      return '获取失败';
    }

    if (videoStatsError && (cardType === 'favorite' || cardType === 'account' || cardType === 'keyword')) {
      return '获取失败';
    }

    switch (cardType) {
      case 'total':
        // 收件箱卡片：直接显示总视频数量
        return '总视频数量';

      case 'favorite':
      case 'account':
      case 'keyword': {
        // 其他卡片：优先使用 API 返回的 latest_times，备用本地计算
        if (videoRelatedStats?.latest_times) {
          const sourceTypeMap = {
            'favorite': 'collect',
            'account': 'author',
            'keyword': 'video'
          } as const;

          const apiSourceType = sourceTypeMap[cardType];
          const latestTime = videoRelatedStats.latest_times[apiSourceType];

          if (latestTime) {
            // 格式化时间显示
            const date = new Date(latestTime);
            const formattedTime = formatDateTime(date);
            return `最后更新：${formattedTime}`;
          } else {
            return '未更新';
          }
        }

        // 备用：使用本地计算的最后更新时间
        const sourceTypeMap = {
          'favorite': 'collect',
          'account': 'author',
          'keyword': 'video'
        } as const;
        const sourceVideos = combinedVideoData.filter(v => v.source_type === sourceTypeMap[cardType as keyof typeof sourceTypeMap]);
        if (sourceVideos.length === 0) return '未更新';

        const latestVideo = sourceVideos.reduce((latest, current) => {
          const latestTime = latest.createTime ? new Date(latest.createTime).getTime() : 0;
          const currentTime = current.createTime ? new Date(current.createTime).getTime() : 0;
          return currentTime > latestTime ? current : latest;
        });

        return `最后更新：${latestVideo.createTime ? formatDateTime(new Date(latestVideo.createTime)) : '未知'}`;
      }

      default:
        return '';
    }
  };

  // 筛选数据
  const filteredData = combinedVideoData.filter(video => {
    if (activeFilter === 'all') return true;
    // 映射过滤条件到API的source_type
    const sourceTypeMap = {
      'favorite': 'collect',
      'account': 'author',
      'keyword': 'video'
    } as const;
    return video.source_type === sourceTypeMap[activeFilter as keyof typeof sourceTypeMap];
  });

  console.log('filteredData: ', filteredData)

  // 排序数据
  const sortedData = [...filteredData].sort((a, b) => {
    if (sortBy === 'addedTime') {
      // 按添加时间排序
      const timeA = a.createTime ? new Date(a.createTime).getTime() : 0;
      const timeB = b.createTime ? new Date(b.createTime).getTime() : 0;
      return timeB - timeA; // 降序
    } else {
      // 按发布时间排序
      const timeA = a.publish_time ? new Date(a.publish_time).getTime() : 0;
      const timeB = b.publish_time ? new Date(b.publish_time).getTime() : 0;
      return timeB - timeA; // 降序
    }
  });

  // 处理筛选变化
  const handleFilterChange = (filter: 'all' | 'favorite' | 'account' | 'keyword') => {
    setActiveFilter(filter);

    // 根据筛选条件重新获取 API 数据
    const sourceTypeMap = {
      'favorite': 'collect' as const,
      'account': 'author' as const,
      'keyword': 'video' as const
    };

    const sourceType = filter === 'all' ? undefined : sourceTypeMap[filter];
    fetchRelatedVideoList(1, pagination.page_size, sourceType, sortBy === 'addedTime' ? 'create_time' : 'publish_time');
  };

  // 处理排序变化
  const handleSortChange = (sort: 'addedTime' | 'publishTime') => {
    setSortBy(sort);

    // 根据排序条件重新获取 API 数据
    const sourceTypeMap = {
      'favorite': 'collect' as const,
      'account': 'author' as const,
      'keyword': 'video' as const
    };

    const sourceType = activeFilter === 'all' ? undefined : sourceTypeMap[activeFilter];
    fetchRelatedVideoList(1, pagination.page_size, sourceType, sort === 'addedTime' ? 'create_time' : 'publish_time');
  };

  const handleImport = (video: VideoItem) => {
    setSelectedVideo(video);
    importModalRef.current?.showModal();
  };

  // 导入状态管理
  const [importingVideos, setImportingVideos] = useState<Set<string>>(new Set());

  const confirmImport = async () => {
    if (!selectedVideo || !selectedVideo.uuid) {
      toast.error('视频信息不完整，无法导入');
      return;
    }

    const recordUuid = selectedVideo.uuid;

    try {
      // 设置导入中状态
      setImportingVideos(prev => new Set(prev).add(recordUuid));

      // 调用真实的API接口
      const { data, error } = await apiClient.POST('/client/user/douyin/addVideoToAssets', {
        body: {
          record_uuid: recordUuid
        }
      });

      if (error) {
        throw new Error(error.msg || '导入素材库失败');
      }

      if (data?.code === 0) {
        // 更新相关视频数据状态，标记为已导入
        setRelatedVideos(prev => prev.map(record =>
          record.uuid === recordUuid
            ? { ...record, handle_status: HANDLE_STATUS.SUCCESS }
            : record
        ));

        const videoTitle = selectedVideo.douyin_aweme?.Title || selectedVideo.douyin_aweme?.Desc || '视频';
        toast.success(`素材「${videoTitle}」已成功导入素材库，开始进行拆片和选题生成`);
        importModalRef.current?.close();
        setSelectedVideo(null);
      } else {
        throw new Error(data?.msg || '导入素材库失败');
      }
    } catch (error: any) {
      console.error('导入素材库失败:', error);

      // 更新状态为失败
      setRelatedVideos(prev => prev.map(record =>
        record.uuid === recordUuid
          ? { ...record, handle_status: HANDLE_STATUS.FAILED }
          : record
      ));

      toast.error(`导入失败: ${error.message}`);
    } finally {
      // 清除导入中状态
      setImportingVideos(prev => {
        const newSet = new Set(prev);
        newSet.delete(recordUuid);
        return newSet;
      });
    }
  };

  const getSourceLabel = (source: VideoItem['source_type'] | 'favorite' | 'account' | 'keyword') => {
    switch (source) {
      case 'collect':
      case 'favorite': return '收藏夹';
      case 'author':
      case 'account': return '监控账号';
      case 'video':
      case 'keyword': return '监控关键词';
      default: return '';
    }
  };

  const formatNumber = (num: number | string) => {
    // 如果是字符串且已经格式化，直接返回
    if (typeof num === 'string' && (num.includes('w') || num.includes('万'))) {
      return num;
    }

    // 转换为数字
    const numValue = typeof num === 'string' ? parseStatNumber(num) : num;

    if (numValue >= 10000) {
      return `${(numValue / 10000).toFixed(1)}万`;
    }
    return numValue.toString();
  };

  // 格式化相对时间
  const formatRelativeTime = (dateString: string | undefined, action: '添加' | '发布') => {
    if (!dateString) return '';

    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();

    // 转换为各种时间单位
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffMonths = Math.floor(diffDays / 30);
    const diffYears = Math.floor(diffDays / 365);

    if (diffMinutes < 60) {
      if (diffMinutes === 0) {
        return `刚刚${action}`;
      }
      return `${diffMinutes}分钟前${action}`;
    } else if (diffHours < 24) {
      return `${diffHours}小时前${action}`;
    } else if (diffDays < 30) {
      return `${diffDays}天前${action}`;
    } else if (diffMonths < 12) {
      return `${diffMonths}个月前${action}`;
    } else {
      return `${diffYears}年前${action}`;
    }
  };

  // 格式化视频时长
  const formatDuration = (seconds: number | undefined): string => {
    if (!seconds || seconds <= 0) return '';

    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;

    if (minutes === 0) {
      return `${remainingSeconds}s`;
    } else if (minutes < 60) {
      return remainingSeconds > 0 ? `${minutes}:${remainingSeconds.toString().padStart(2, '0')}` : `${minutes}:00`;
    } else {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return `${hours}:${remainingMinutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
  };

  const formatHotIndex = (index?: number | null) => {
    console.log('formatHotIndex: ', index)
    if (!index && index !== 0) return '尚未更新热度指数';
    if (index >= 10000) {
      return `当前热度指数 ${(index / 10000).toFixed(1)}万`;
    }
    return `当前热度指数 ${index}`;
  };

  // 获取视频 URL 的工具方法
  const getVideoUrl = (video: VideoItem) => {
    const displayInfo = getVideoDisplayInfo(video);
    
    // 优先使用 awemeUrl（来自详情或 douyin_aweme）
    if (displayInfo.awemeUrl) {
      return displayInfo.awemeUrl;
    }
    
    // 备用方案：使用默认 URL
    return `https://www.douyin.com/video/${video.aweme_id || ''}`;
  };


  const openAuthorPage = (video: VideoItem) => {
    const displayInfo = getVideoDisplayInfo(video);

    // 如果有 sec_uid，直接打开用户主页
    if (displayInfo.userSecUid) {
      const userUrl = `https://www.douyin.com/user/${displayInfo.userSecUid}`;
      window.open(userUrl, '_blank');
      toast.info(`正在打开 ${displayInfo.author} 的主页`);
      return;
    }

    // 备用方案：搜索用户
    const searchUrl = `https://www.douyin.com/search/${encodeURIComponent(displayInfo.author)}?type=user`;
    window.open(searchUrl, '_blank');
    toast.info(`正在搜索 ${displayInfo.author} 的主页`);
  };

  // 获取视频的显示信息（使用 douyin_aweme 数据）
  const getVideoDisplayInfo = (video: VideoItem) => {
    const awemeData = video.douyin_aweme;

    return {
      title: awemeData?.Title || awemeData?.Desc || '无标题',
      author: awemeData?.Nickname || '未知作者',
      thumbnail: awemeData?.CoverURL || '',
      stats: awemeData ? {
        likes: parseStatNumber(awemeData.LikedCount),
        comments: parseStatNumber(awemeData.CommentCount),
        shares: parseStatNumber(awemeData.ShareCount),
        views: parseStatNumber(awemeData.CollectedCount),
      } : {
        likes: 0,
        comments: 0,
        shares: 0,
        views: 0,
      },
      isLoading: false,
      // 新增字段：提供更多信息
      awemeUrl: awemeData?.AwemeURL,
      userAvatar: awemeData?.Avatar,
      userSecUid: awemeData?.SecUID,
      create_time: awemeData?.CreateTime,
      videoDownloadUrl: awemeData?.VideoDownloadURL,
      // 额外的视频信息
      duration: undefined,
      videoWidth: undefined,
      videoHeight: undefined,
      userId: awemeData?.UserID
    };
  };

  return (
    <div>
      <header className="mb-6">
        <h1 className="text-2xl font-bold">灵感收件箱</h1>
        <p className="text-base-content opacity-60">汇聚所有灵感来源，发现创作素材</p>
      </header>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <div className="stat bg-base-100 shadow rounded-box">
          <div className="stat-figure text-primary">
            <i className="fas fa-inbox text-2xl"></i>
          </div>
          <div className="stat-title">收件箱</div>
          <div className="stat-value text-primary">
            {(isLoadingStats || isLoadingVideoStats) ? (
              <span className="loading loading-spinner loading-sm"></span>
            ) : (
              stats.total
            )}
          </div>
          <div className="stat-desc">{getCardDescription('total')}</div>
        </div>

        <div className="stat bg-base-100 shadow rounded-box">
          <div className="stat-figure text-secondary">
            <i className="fas fa-star text-2xl"></i>
          </div>
          <div className="stat-title">收藏夹</div>
          <div className="stat-value text-secondary">{stats.favorite}</div>
          <div className="stat-desc">{getCardDescription('favorite')}</div>
        </div>

        <div className="stat bg-base-100 shadow rounded-box">
          <div className="stat-figure text-info">
            <i className="fas fa-eye text-2xl"></i>
          </div>
          <div className="stat-title">监控账号</div>
          <div className="stat-value text-info">{stats.account}</div>
          <div className="stat-desc">{getCardDescription('account')}</div>
        </div>

        <div className="stat bg-base-100 shadow rounded-box">
          <div className="stat-figure text-accent">
            <i className="fas fa-search text-2xl"></i>
          </div>
          <div className="stat-title">监控关键词</div>
          <div className="stat-value text-accent">{stats.keyword}</div>
          <div className="stat-desc">{getCardDescription('keyword')}</div>
        </div>
      </div>

      {/* 灵感内容列表 */}
      <Card className="bg-base-100 shadow-md">
        <Card.Body>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-4">
            <div className="flex items-center gap-3">
              <h3 className="text-xl font-bold">最新灵感</h3>
              {isLoadingRelated && (
                <span className="loading loading-spinner loading-sm"></span>
              )}
            </div>
            <div className="flex flex-col sm:flex-row gap-2 items-start sm:items-center w-full sm:w-auto">
              <div className="flex flex-wrap gap-2">
                <Button
                  size="xs"
                  color={activeFilter === 'all' ? 'neutral' : 'ghost'}
                  onClick={() => handleFilterChange('all')}
                  disabled={isLoadingRelated}
                >
                  全部
                </Button>
                <Button
                  size="xs"
                  color={activeFilter === 'favorite' ? 'neutral' : 'ghost'}
                  onClick={() => handleFilterChange('favorite')}
                  disabled={isLoadingRelated}
                >
                  收藏夹
                </Button>
                <Button
                  size="xs"
                  color={activeFilter === 'account' ? 'neutral' : 'ghost'}
                  onClick={() => handleFilterChange('account')}
                  disabled={isLoadingRelated}
                >
                  监控账号
                </Button>
                <Button
                  size="xs"
                  color={activeFilter === 'keyword' ? 'neutral' : 'ghost'}
                  onClick={() => handleFilterChange('keyword')}
                  disabled={isLoadingRelated}
                >
                  监控关键词
                </Button>
              </div>
              <div className="divider divider-horizontal mx-2 hidden sm:flex"></div>
              <div className="flex flex-wrap gap-2">
                <Button
                  size="xs"
                  color={sortBy === 'addedTime' ? 'neutral' : 'ghost'}
                  onClick={() => handleSortChange('addedTime')}
                  disabled={isLoadingRelated}
                >
                  <i className="fas fa-clock mr-1"></i>
                  <span className="hidden sm:inline">按添加时间</span>
                  <span className="sm:hidden">添加时间</span>
                </Button>
                <Button
                  size="xs"
                  color={sortBy === 'publishTime' ? 'neutral' : 'ghost'}
                  onClick={() => handleSortChange('publishTime')}
                  disabled={isLoadingRelated}
                >
                  <i className="fas fa-calendar mr-1"></i>
                  <span className="hidden sm:inline">按发布时间</span>
                  <span className="sm:hidden">发布时间</span>
                </Button>
              </div>
            </div>
          </div>


          {videoStatsError && (
            <div className="alert alert-warning mb-4">
              <i className="fas fa-exclamation-triangle"></i>
              <div>
                <div className="font-medium">获取视频统计数据失败</div>
                <div className="text-sm opacity-80">{videoStatsError}</div>
              </div>
              <Button
                size="xs"
                className="btn-outline"
                onClick={() => fetchVideoRelatedStats(true)}
                disabled={isLoadingVideoStats}
              >
                重试
              </Button>
            </div>
          )}

          {relatedError && (
            <div className="alert alert-error mb-4">
              <i className="fas fa-exclamation-triangle"></i>
              <div>
                <div className="font-medium">获取视频列表失败</div>
                <div className="text-sm opacity-80">{relatedError}</div>
              </div>
              <Button
                size="xs"
                className="btn-outline"
                onClick={() => fetchRelatedVideoList(pagination.page, pagination.page_size)}
              >
                重试
              </Button>
            </div>
          )}

          {/* 视频网格 */}
          <div className="grid gap-4" style={{ gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))' }}>
            {sortedData.length === 0 ? (
              <div className="col-span-full text-center py-12">
                <i className="fas fa-inbox text-6xl opacity-30 mb-4"></i>
                <h3 className="text-xl font-bold mb-2">暂无灵感内容</h3>
                <p className="opacity-60">
                  {isLoadingRelated
                    ? '正在加载灵感内容...'
                    : activeFilter === 'all'
                    ? '暂无灵感内容，请检查收藏夹和监控设置，或稍后刷新重试'
                    : `当前筛选条件下暂无${getSourceLabel(activeFilter)}内容`
                  }
                </p>
              </div>
            ) : (
              sortedData.map(video => {
                const displayInfo = getVideoDisplayInfo(video);
                return (
                  <div key={video.uuid || video.aweme_id || ''} className="inspiration-card bg-base-100 p-3 flex flex-col">
                    {/* 视频缩略图 */}
                    <a href={getVideoUrl(video)} target="_blank" rel="noopener noreferrer" className="relative mb-3 block">
                      {(
                        <div className="absolute top-2 left-2 z-10 rounded-lg border border-white/10 bg-black/60 px-2 py-1 text-xs font-medium text-white opacity-90 backdrop-blur-sm">
                          {formatHotIndex(video.trend_score || 0)}
                        </div>
                      )}
                      {/* 显示视频时长 */}
                      {displayInfo.duration && (
                        <div className="absolute bottom-2 right-2 z-10 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded backdrop-blur-sm border border-white border-opacity-10">
                          {formatDuration(displayInfo.duration)}
                        </div>
                      )}
                      {displayInfo.isLoading && (
                        <div className="absolute top-2 right-2 z-10">
                          <span className="loading loading-spinner loading-sm bg-white bg-opacity-80 rounded-full p-1"></span>
                        </div>
                      )}
                      <img
                        src={displayInfo.thumbnail || ''}
                        alt={displayInfo.title}
                        className="w-full aspect-[4/3] object-cover rounded-lg"
                        onError={(e) => {
                          // 图片加载失败时的备用处理
                          (e.target as HTMLImageElement).src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQwIiBoZWlnaHQ9IjQ4MCIgdmlld0JveD0iMCAwIDY0MCA0ODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2NDAiIGhlaWdodD0iNDgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0zMjAgMjQwTDM2MCAyMDBIMjgwTDMyMCAyNDBaIiBmaWxsPSIjOUI5QkEzIi8+Cjx0ZXh0IHg9IjMyMCIgeT0iMjcwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOUI5QkEzIiBmb250LXNpemU9IjE0Ij7lm77niYfmlKDovb3lpLHotKU8L3RleHQ+Cjwvc3ZnPg==';
                        }}
                      />
                      {/* 播放按钮覆盖层 */}
                      <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 opacity-0 hover:opacity-100 transition-opacity rounded-lg">
                        <div className="w-12 h-12 bg-white bg-opacity-90 rounded-full flex items-center justify-center text-gray-800 text-lg hover:bg-white hover:scale-110 transition-all">
                          <i className="fas fa-play"></i>
                        </div>
                      </div>
                    </a>

                    {/* 视频信息 */}
                    <div className="flex-1 flex flex-col">

                      {/* 标题 */}
                      <a href={getVideoUrl(video)} target="_blank" rel="noopener noreferrer">
                        <h4
                          className="font-medium text-sm leading-5 mb-2 cursor-pointer hover:underline"
                          style={{
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            minHeight: '2.5em'
                          }}
                        >
                          {displayInfo.title}
                        </h4>
                      </a>

                    {/* 作者和时间信息 */}
                    <div className="flex items-center justify-between text-xs opacity-60 mb-3">
                      <div className="flex items-center gap-2">
                        {displayInfo.userAvatar && (
                          <div className="avatar">
                            <div className="w-4 h-4 rounded-full overflow-hidden">
                              <img
                                src={displayInfo.userAvatar}
                                alt="作者头像"
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  // 头像加载失败时隐藏
                                  (e.target as HTMLImageElement).style.display = 'none';
                                }}
                              />
                            </div>
                          </div>
                        )}
                        <span
                          className="cursor-pointer hover:underline"
                          onClick={(e) => {
                            e.stopPropagation();
                            openAuthorPage(video);
                          }}
                        >
                          {displayInfo.author}
                        </span>
                      </div>
                      <div className="flex items-center gap-1">
                        <span>
                          {sortBy === 'addedTime'
                            ? formatRelativeTime(video.createTime, '添加')
                            : formatRelativeTime(video.publish_time, '发布')
                          }
                        </span>
                        <span>·</span>
                        <span>{getSourceLabel(video.source_type || 'collect')}</span>
                      </div>
                    </div>

                    {/* 数据统计 */}
                    <div className="flex items-center justify-between text-xs">
                      <div className="flex gap-3">
                        <span className="flex items-center gap-1 opacity-60">
                          <i className="fas fa-heart"></i>
                          {formatNumber(displayInfo.stats.likes)}
                        </span>
                        <span className="flex items-center gap-1 opacity-60">
                          <i className="fas fa-comment"></i>
                          {formatNumber(displayInfo.stats.comments)}
                        </span>
                        <span className="flex items-center gap-1 opacity-60">
                          <i className="fas fa-star"></i>
                          {formatNumber(displayInfo.stats.shares)}
                        </span>
                        <span className="flex items-center gap-1 opacity-60">
                          <i className="fas fa-share"></i>
                          {formatNumber(displayInfo.stats.views)}
                        </span>
                      </div>
                      <div className="flex items-center gap-1">
                        {/* 导入按钮状态处理 */}
                        {(() => {
                          const recordUuid = video.uuid;
                          const isImporting = recordUuid && importingVideos.has(recordUuid);

                          if (video.handle_status === HANDLE_STATUS.SUCCESS) {
                            return <Button className="text-white" color="success" size="xs">已导入</Button>;
                          } else if (isImporting) {
                            return (
                              <Button size="xs" disabled>
                                <span className="loading loading-spinner loading-xs mr-1"></span>
                                导入中...
                              </Button>
                            );
                          } else if (video.handle_status === HANDLE_STATUS.FAILED) {
                            return (
                              <div className="flex items-center gap-1">
                                <Badge color="error" size="xs">导入失败</Badge>
                                <Button
                                  size="xs"
                                  className="btn-outline"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleImport(video);
                                  }}
                                  disabled={!!(video.uuid && importingVideos.has(video.uuid))}
                                >
                                  {video.uuid && importingVideos.has(video.uuid) ? (
                                    <>
                                      <span className="loading loading-spinner loading-xs mr-1"></span>
                                      导入中...
                                    </>
                                  ) : (
                                    '重试'
                                  )}
                                </Button>
                              </div>
                            );
                          } else {
                            return (
                              <Button
                                className={video.handle_status === HANDLE_STATUS.PENDING ? '' : 'btn-outline'}
                                size="xs"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleImport(video);
                                }}
                                disabled={!!(video.uuid && importingVideos.has(video.uuid))}
                              >
                                {video.uuid && importingVideos.has(video.uuid) ? (
                                  <>
                                    <span className="loading loading-spinner loading-xs mr-1"></span>
                                    导入中...
                                  </>
                                ) : (
                                  '导入素材库'
                                )}
                              </Button>
                            );
                          }
                        })()}
                      </div>
                    </div>
                  </div>
                </div>
                );
              })
            )}
          </div>

          {/* 分页控件 */}
          {pagination.total > 0 && (
            <div className="flex items-center justify-between mt-6 pt-4 border-t border-base-300">
              <div className="text-sm text-base-content opacity-60">
                共 {pagination.total} 条记录，第 {pagination.page} 页
              </div>
              <div className="flex items-center gap-2">
                <Button
                  size="xs"
                  className="btn-outline"
                  disabled={pagination.page <= 1 || isLoadingRelated}
                  onClick={() => {
                    const sourceTypeMap = {
                      'favorite': 'collect' as const,
                      'account': 'author' as const,
                      'keyword': 'video' as const
                    };
                    const sourceType = activeFilter === 'all' ? undefined : sourceTypeMap[activeFilter];
                    fetchRelatedVideoList(
                      pagination.page - 1,
                      pagination.page_size,
                      sourceType,
                      sortBy === 'addedTime' ? 'create_time' : 'publish_time'
                    );
                  }}
                >
                  上一页
                </Button>
                <Button
                  size="xs"
                  className="btn-outline"
                  disabled={pagination.page >= Math.ceil(pagination.total / pagination.page_size) || isLoadingRelated}
                  onClick={() => {
                    const sourceTypeMap = {
                      'favorite': 'collect' as const,
                      'account': 'author' as const,
                      'keyword': 'video' as const
                    };
                    const sourceType = activeFilter === 'all' ? undefined : sourceTypeMap[activeFilter];
                    fetchRelatedVideoList(
                      pagination.page + 1,
                      pagination.page_size,
                      sourceType,
                      sortBy === 'addedTime' ? 'create_time' : 'publish_time'
                    );
                  }}
                >
                  下一页
                </Button>
              </div>
            </div>
          )}
        </Card.Body>
      </Card>

      {/* 导入确认模态框 */}
      <Modal ref={importModalRef} containerClasses="modal-bottom sm:modal-middle">
        <Modal.Header className="text-lg font-bold mb-4">导入素材库</Modal.Header>
        <Modal.Body>
          <p className="mb-4">确认将「<span className="font-medium">{selectedVideo?.douyin_aweme?.Title || selectedVideo?.douyin_aweme?.Desc || '视频'}</span>」导入素材库吗？</p>
          <div className="alert alert-info">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" className="h-6 w-6 shrink-0 stroke-current">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z" />
            </svg>
            <span>导入素材库后将开始进行拆片和选题生成</span>
          </div>
        </Modal.Body>
        <Modal.Actions>
          <form method="dialog" className="flex gap-2">
            <Button
              onClick={() => importModalRef.current?.close()}
              disabled={!!(selectedVideo?.uuid && importingVideos.has(selectedVideo.uuid))}
            >
              取消
            </Button>
            <Button
              className="bg-black text-white hover:bg-gray-800"
              onClick={confirmImport}
              disabled={!!(selectedVideo?.uuid && importingVideos.has(selectedVideo.uuid))}
            >
              {selectedVideo?.uuid && importingVideos.has(selectedVideo.uuid) ? (
                <>
                  <span className="loading loading-spinner loading-xs mr-1"></span>
                  导入中...
                </>
              ) : (
                '确认导入'
              )}
            </Button>
          </form>
        </Modal.Actions>
      </Modal>
    </div>
  );
};

export default Inbox;

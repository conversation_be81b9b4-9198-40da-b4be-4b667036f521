import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Input, Join } from 'react-daisyui';
import { useCozeStore } from '../models/coze';
import { useAuthStore } from '@/models/auth';
import clsx from 'clsx';
import { getAppConfig, getToolsBotList } from '@/api/common';

// 定义工具项的接口
interface ToolItem {
  uuid: string;
  avatar: string;
  name: string;
  type: string;
  toolParam: any;
  description?: string;
  isPrivate: boolean;
  tags?: string[];
}

// 声明全局的CozeWebSDK
declare global {
  interface Window {
    CozeWebSDK: {
      WebChatClient: new (config: any) => {
        destroy: () => void;
        showChatBot: () => void;
      };
    };
  }
}

const Tools: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [filteredList, setFilteredList] = useState<ToolItem[]>([]);
  const [activeTag, setActiveTag] = useState<string>('全部');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [tagsList, setTagsList] = useState<{id: string, name: string}[]>([
    { id: '全部', name: '全部' },
  ]);
  const cozeRef = useRef<{ destroy: () => void; showChatBot: () => void } | null>(null);
  const { actions } = useCozeStore();
  const { state } = useAuthStore();

  // 获取标签列表
  const fetchTags = async () => {
    try {
      const response = await getAppConfig();
      if (response.data.data?.tools?.toolTypes) {
        const toolTypesString = response.data.data.tools.toolTypes;
        const toolTypes = toolTypesString.split('、');
        
        const newTagsList = [
          { id: '全部', name: '全部' },
          ...toolTypes.map((type: string) => ({ id: type, name: type }))
        ];
        
        setTagsList(newTagsList);
        
        // 更新CSS样式
        updateTagStyles(toolTypes);
      }
    } catch (error) {
      console.error('获取标签列表失败:', error);
    }
  };

  // 更新标签对应的CSS样式
  const updateTagStyles = (tags: string[]) => {
    const colors = [
      { bg: 'rgba(var(--p), 0.2)', text: 'rgba(var(--p), 1)' },
      { bg: 'rgba(var(--s), 0.2)', text: 'rgba(var(--s), 1)' },
      { bg: 'rgba(var(--a), 0.2)', text: 'rgba(var(--a), 1)' },
    ];
    
    // 基础样式
    let cssString = `
    .tag-filter {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
      margin-bottom: 1.5rem;
    }
    
    .tag-btn {
      font-size: 0.85rem !important;
      padding: 0.35rem 0.75rem !important;
      height: auto !important;
      min-height: 2rem !important;
    }
    
    .badge-soft {
      font-weight: normal;
      padding: 0.35rem 0.5rem;
      font-size: 0.7rem;
    }
    `;
    
    // 为每个标签添加对应的样式
    tags.forEach((tag, index) => {
      const colorIndex = index % colors.length;
      cssString += `
      .badge-${tag} {
        background-color: ${colors[colorIndex].bg} !important;
        color: ${colors[colorIndex].text} !important;
      }
      `;
    });
    
    // 更新style元素的内容
    styleEl.innerHTML = cssString;
  };

  const fetchData = async (tag = '', name = '') => {
    try {
      setLoading(true);
      
      const params: Record<string, string> = {};
      if (tag) params.search_tag = tag;
      if (name) params.search_name = name;
      
      const response = await getToolsBotList({ params });
      
      const toolsData = response.data.data || [];
      setFilteredList(toolsData);
      setLoading(false);
    } catch (error) {
      console.error('获取工具列表失败:', error);
      setLoading(false);
    }
  };

  const handleUse = async (toolItem: ToolItem) => {
    if (cozeRef.current) {
      cozeRef.current.destroy();
    }

    const token = await actions.onceGetSessionToken();
    
    // 确保CozeWebSDK是可用的
    if (typeof window.CozeWebSDK === 'undefined') {
      console.error('CozeWebSDK not found');
      return;
    }

    
    cozeRef.current = new window.CozeWebSDK.WebChatClient({
      config: {
        ...toolItem.toolParam,
        isIframe: false,
      },
      auth: {
        type: 'token',
        token: token,
        onRefreshToken: async () => await actions.getSessionToken(),
      },
      userInfo: {
        id: state.userInfo?.uuid,
        url: state.userInfo?.wx_avatar,
        nickname: state.userInfo?.wx_nickname
      },
      ui: {
        base: {
          lang: 'zh-CN',
          icon: toolItem.avatar,
          zIndex: 1000
        },
        header: {
          isShow: true,
          isNeedClose: true
        },
        asstBtn: {
          isNeed: false
        },
        footer: {
          isShow: true,
          expressionText: '由 {{qihaozhushou}} 基于扣子提供技术支持',
          linkvars: {
            qihaozhushou: {
              text: '起号助手',
              link: 'https://www.qihaozhushou.com'
            }
          }
        },
        chatBot: {
          title: toolItem.name,
          uploadable: true,
          width: window.innerWidth > 800 ? 800 : window.innerWidth - 40,
          onHide: () => {
            console.log('聊天框已隐藏');
          },
          onShow: () => {
            console.log('聊天框已显示');
          },
          onBeforeShow: () => {
            console.log('聊天框即将显示');
            return true;
          },
          onBeforeHide: () => {
            console.log('聊天框即将隐藏');
            return true;
          }
        }
      }
    });
    cozeRef.current.showChatBot();
  };

  // 处理标签点击
  const handleTagClick = (tag: string) => {
    setActiveTag(tag);
    // 当点击标签时，发起请求获取数据，保留搜索值
    fetchData(tag !== '全部' ? tag : '', searchQuery);
  };

  // 处理搜索
  const handleSearch = () => {
    // 进行搜索，保留当前选中的标签
    fetchData(activeTag !== '全部' ? activeTag : '', searchQuery);
  };

  // 初始化组件
  const initializeComponent = async () => {
    try {
      setLoading(true);
      // 先获取标签列表
      await fetchTags();
      // 再获取工具列表（不带任何筛选条件）
      await fetchData('', '');
    } catch (error) {
      console.error('初始化组件失败:', error);
      setLoading(false);
    }
  };

  useEffect(() => {
    initializeComponent();
  }, []);

  // 添加CSS样式到页面头部
  const styleEl = document.createElement('style');
  
  useEffect(() => {
    // 添加样式到文档
    document.head.appendChild(styleEl);
    
    // 组件卸载时移除样式
    return () => {
      document.head.removeChild(styleEl);
    };
  }, []);

  return (
    <div>
      <header className="mb-6">
        <h1 className="text-2xl font-bold">智能体</h1>
        <p className="text-base-content opacity-60">让每一次创作都是突破，每一个作品都有价值</p>
      </header>

      <div className="mb-8">

        {/* 标签筛选和搜索区 */}
        <div className="flex flex-col sm:flex-row justify-between gap-4 mb-6">
          {/* 标签筛选 */}
          <div className="flex flex-wrap gap-2 mb-4 sm:mb-0 tag-filter">
            {tagsList.map(tag => (
              <Button 
                key={tag.id} 
                size="sm" 
                color={activeTag === tag.id ? 'neutral' : 'ghost'}
                variant="outline"
                className={clsx(
                  "font-bold",
                  "tag-btn",
                  activeTag === tag.id ? 'bg-neutral text-white' : ''
                )}
                onClick={() => handleTagClick(tag.id)}
              >
                {tag.name}
              </Button>
            ))}
          </div>
          
          {/* 搜索区 */}
          <div className="flex-shrink-0">
            <Join>
              <Input 
                className="w-64 md:w-80 lg:w-96" 
                placeholder="搜索智能体名称..." 
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              />
              <Button onClick={handleSearch}>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </Button>
            </Join>
          </div>
        </div>

        <div className={clsx("grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mb-2", { '!hidden': !loading })}>
          {Array(3).fill(0).map((_, index) => (
            <div key={index} className="card shadow-sm hover:shadow-md transition-shadow card min-h-32 bg-base-100 card-bordered animate-pulse">
              <div className="card-body p-4">
                <div className="flex items-center mb-3">
                  <div className="bg-base-300 rounded-full w-10 h-10 flex items-center justify-center mr-3"></div>
                  <div className="h-4 bg-base-300 rounded w-1/3"></div>
                </div>
                <div className="space-y-2">
                  <div className="h-3 bg-base-300 rounded w-full"></div>
                  <div className="h-3 bg-base-300 rounded w-5/6"></div>
                  <div className="h-3 bg-base-300 rounded w-4/6"></div>
                </div>
                <div className="card-actions justify-between items-center mt-3">
                  <div className="h-4 bg-base-300 rounded w-16"></div>
                  <div className="h-8 bg-base-300 rounded w-20"></div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {filteredList.map(toolItem => (
            <Card 
              key={toolItem.uuid} 
              className={clsx('shadow-sm hover:shadow-md transition-shadow card min-h-32 bg-base-100')}
              onClick={() => handleUse(toolItem)}
              data-tags={toolItem.type?.split('、')}
            >
              <Card.Body className="p-4">
                <div className="flex items-center mb-3">
                  <div className="bg-base-200 rounded-full w-10 h-10 flex items-center justify-center mr-3 overflow-hidden">
                    <img className="w-full h-full object-cover" src={toolItem.avatar} alt={toolItem.name} />
                  </div>
                  <h3 className="card-title text-base">{toolItem.name}</h3>
                </div>
                <p className="text-sm opacity-70">{toolItem.description}</p>
                <Card.Actions className="flex-wrap justify-between items-center mt-3">
                  {toolItem.type && toolItem.type.split('、').map(tag => (
                    <Badge 
                      key={tag} 
                      className={clsx(
                        "badge-soft mr-1 mb-1",
                        `badge-智能体`
                      )}
                      color="ghost"
                    >
                      {tag}
                    </Badge>
                  ))}
                  <Button color="neutral" size="sm" className="ml-auto">使用</Button>
                </Card.Actions>
              </Card.Body>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Tools;
import React, { useRef, useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON> } from 'react-daisyui';
import { createPayOrder, getAppConfig, refreshPayQRCode, cancelOrder, getOrderStatus } from '../api/common';
import { useNavigate } from 'react-router-dom';
import './price.css';

interface AppConfig {
  pay: {
    display: {
      paid: {
        originalPrice: string;
        paymentPrice: string;
      }
    }
  }
}

interface OrderData {
  amount: number;
  expire_time: string;
  order_no: string;
  payment_qr: string;
  subject: string;
}

interface OrderStatus {
  amount: number;
  expire_time: string;
  is_expired: boolean;
  order_no: string;
  status: 'PAID' | 'EXPIRED' | 'CANCELLED';
  subject: string;
}

const Price: React.FC = () => {
  const navigate = useNavigate();
  const paymentModalRef = useRef<HTMLDialogElement>(null);
  const successModalRef = useRef<HTMLDialogElement>(null);
  const [countdown, setCountdown] = useState("1:00");
  const [priceConfig, setPriceConfig] = useState<{originalPrice: string, paymentPrice: string}>({
    originalPrice: "6980",
    paymentPrice: "780"
  });
  const [loading, setLoading] = useState(true);
  const [orderData, setOrderData] = useState<OrderData | null>(null);
  const [orderLoading, setOrderLoading] = useState(false);
  const countdownIntervalRef = useRef<number | null>(null);
  const orderStatusIntervalRef = useRef<number | null>(null);
  
  useEffect(() => {
    const fetchConfig = async () => {
      try {
        setLoading(true);
        const response = await getAppConfig();
        const data = response.data.data as AppConfig;
        if (data && data.pay && data.pay.display && data.pay.display.paid) {
          setPriceConfig({
            originalPrice: data.pay.display.paid.originalPrice,
            paymentPrice: data.pay.display.paid.paymentPrice
          });
        }
      } catch (error) {
        console.error('Failed to fetch price config:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchConfig();
  }, []);
  
  const createOrder = async () => {
    try {
      setOrderLoading(true);
      const response = await createPayOrder({
        params: {
          role: "paid"
        }
      });
      
      if (response.data.code === 0) {
        const newOrderData = response.data.data;
        setOrderData(newOrderData);
        startCountdown();
        startOrderStatusPolling(newOrderData.order_no);
      } else {
        console.error('Error creating order:', response.data.message);
      }
    } catch (error) {
      console.error('Failed to create order:', error);
    } finally {
      setOrderLoading(false);
    }
  };
  
  const handlePayment = async () => {
    await createOrder();
    paymentModalRef.current?.showModal();
  };
  
  const handleRefreshQRCode = async () => {
    try {
      if (orderData?.order_no) {
        // If we have an existing order number, refresh the QR code
        setOrderLoading(true);
        const response = await refreshPayQRCode({
          params: {
            order_no: orderData.order_no
          }
        });
        
        if (response.data.code === 0) {
          setOrderData({
            ...orderData,
            payment_qr: response.data.data.payment_qr,
            order_no: response.data.data.order_no
          });
          startCountdown();
        } else {
          console.error('Error refreshing QR code:', response.data.message);
        }
      } else {
        // If no order exists yet, create a new one
        await createOrder();
      }
    } catch (error) {
      console.error('Failed to refresh QR code:', error);
    } finally {
      setOrderLoading(false);
    }
  };
  
  const handleShowPaymentSuccess = () => {
    paymentModalRef.current?.close();
    if (countdownIntervalRef.current) {
      clearInterval(countdownIntervalRef.current);
      countdownIntervalRef.current = null;
    }
    setTimeout(() => {
      successModalRef.current?.showModal();
    }, 500);
  };
  
  const startCountdown = () => {
    // Clear any existing interval
    if (countdownIntervalRef.current) {
      clearInterval(countdownIntervalRef.current);
    }
    
    let seconds = 60;
    setCountdown("1:00");
    
    countdownIntervalRef.current = setInterval(() => {
      seconds--;
      
      if (seconds <= 0) {
        if (countdownIntervalRef.current) {
          clearInterval(countdownIntervalRef.current);
          countdownIntervalRef.current = null;
        }
        setCountdown("0:00");
        return;
      }
      
      const formattedSeconds = seconds < 10 ? `0${seconds}` : `${seconds}`;
      setCountdown(`0:${formattedSeconds}`);
    }, 1000);
  };
  
  const startOrderStatusPolling = (orderNo: string) => {
    // Clear any existing interval
    if (orderStatusIntervalRef.current) {
      clearInterval(orderStatusIntervalRef.current);
      orderStatusIntervalRef.current = null;
    }

    const checkOrderStatus = async () => {
      console.log('Checking order status for:', orderNo);
      try {
        const response = await getOrderStatus({
          params: {
            order_no: orderNo
          }
        });

        console.log('Order status response:', response.data);
        if (response.data.code === 0) {
          const orderStatus = response.data.data as OrderStatus;
          
          if (orderStatus.status === 'PAID') {
            console.log('Order is paid');
            // Stop polling and show success modal
            if (orderStatusIntervalRef.current) {
              clearInterval(orderStatusIntervalRef.current);
              orderStatusIntervalRef.current = null;
            }
            handleShowPaymentSuccess();
          } else if (orderStatus.status === 'EXPIRED' || orderStatus.status === 'CANCELLED' || orderStatus.is_expired) {
            console.log('Order is expired or cancelled');
            // Stop polling and show expired message
            if (orderStatusIntervalRef.current) {
              clearInterval(orderStatusIntervalRef.current);
              orderStatusIntervalRef.current = null;
            }
            alert(orderStatus.status === 'CANCELLED' ? '订单已取消' : '订单已过期');
            handleCloseModal();
          } else {
            console.log('Order status:', orderStatus.status);
          }
        }
      } catch (error) {
        console.error('Failed to check order status:', error);
      }
    };

    // Check immediately first
    checkOrderStatus();
    
    // Then set up the interval
    const intervalId = window.setInterval(checkOrderStatus, 2000);
    orderStatusIntervalRef.current = intervalId;
    console.log('Started order status polling with interval:', intervalId);
  };
  
  // Clean up intervals on component unmount
  useEffect(() => {
    return () => {
      if (countdownIntervalRef.current) {
        clearInterval(countdownIntervalRef.current);
        countdownIntervalRef.current = null;
      }
      if (orderStatusIntervalRef.current) {
        console.log('Cleaning up order status polling interval:', orderStatusIntervalRef.current);
        clearInterval(orderStatusIntervalRef.current);
        orderStatusIntervalRef.current = null;
      }
    };
  }, []);
  
  const handleCloseModal = async () => {
    // Cancel the order if it exists
    if (orderData?.order_no) {
      try {
        await cancelOrder({
          params: {
            order_no: orderData.order_no
          }
        });
        console.log('Order cancelled successfully');
      } catch (error) {
        console.error('Error cancelling order:', error);
      }
    }
    
    // Clear intervals and close modal
    if (countdownIntervalRef.current) {
      clearInterval(countdownIntervalRef.current);
      countdownIntervalRef.current = null;
    }
    if (orderStatusIntervalRef.current) {
      clearInterval(orderStatusIntervalRef.current);
      orderStatusIntervalRef.current = null;
    }
    paymentModalRef.current?.close();
  };
  
  const handleSuccessModalClose = () => {
    successModalRef.current?.close();
    navigate('/dashboard');
  };
  
  return (
    <div>
      <header className="mb-6">
        <h1 className="text-2xl font-bold">付费方案</h1>
        <p className="text-base-content opacity-60">小小投入，一百倍回报，一步到位解锁创作捷径</p>
      </header>

      {/* 价格卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        {/* 免费版 */}
        <Card className="bg-base-100 shadow-md pricing-card">
          <Card.Body>
            <Card.Title tag="h2" className="text-xl mb-2">免费版</Card.Title>
            <p className="opacity-70 mb-6">适合初次体验的用户</p>
            <span className="price-tag mb-6">¥0</span>
            
            <div className="divider"></div>
            
            <div className="mb-6">
              <div className="feature-check">
                <i className="fas fa-check-circle"></i>
                <span>公开智能体 有限使用</span>
              </div>
              <div className="feature-cross">
                <i className="fas fa-times-circle"></i>
                <span>私有智能体定制</span>
              </div>
              <div className="feature-cross">
                <i className="fas fa-times-circle"></i>
                <span>自动采集抖音收藏夹</span>
              </div>
              <div className="feature-cross">
                <i className="fas fa-times-circle"></i>
                <span>自动分析参考素材</span>
              </div>
              <div className="feature-cross">
                <i className="fas fa-times-circle"></i>
                <span>自动生成爆款选题</span>
              </div>
              <div className="feature-cross">
                <i className="fas fa-times-circle"></i>
                <span>多模态纯视频理解和拆片</span>
              </div>
            </div>
            
            <Card.Actions className="justify-center mt-auto">
              <Button variant="outline" className="w-full">当前方案</Button>
            </Card.Actions>
          </Card.Body>
        </Card>
        
        {/* 付费版 */}
        <Card className="bg-base-100 shadow-md border-2 border-black pricing-card">
          <Card.Body>
            <div className="absolute -top-3 -right-3 badge badge-lg bg-black text-white px-3 py-3">推荐</div>
            <Card.Title tag="h2" className="text-xl mb-2">付费版</Card.Title>
            <p className="opacity-70 mb-6">适合认真创作的专业用户</p>
            <div className="mb-6">
              <div className="flex items-end">
                <span className="price-tag">¥{loading ? '...' : priceConfig.paymentPrice}</span>
                <span className="text-xs opacity-70 ml-1" style={{ transform: 'translateY(-12px)' }}>/年</span>
              </div>
              <p className="price-original mt-1">原价 ¥{loading ? '...' : priceConfig.originalPrice}/年</p>
              <div className="badge badge-warning mt-2">早鸟价</div>
              <p className="text-sm mt-2 opacity-70">每月1日和15日涨价</p>
            </div>
            
            <div className="divider"></div>
            
            <div className="mb-6">
              <div className="feature-check">
                <i className="fas fa-check-circle"></i>
                <span>公开智能体 无限量</span>
              </div>
              <div className="feature-check">
                <i className="fas fa-check-circle"></i>
                <span>私有智能体定制 1个</span>
              </div>
              <div className="feature-check">
                <i className="fas fa-check-circle"></i>
                <span>自动采集抖音收藏夹 无限量</span>
              </div>
              <div className="feature-check">
                <i className="fas fa-check-circle"></i>
                <span>自动分析参考素材 无限量</span>
              </div>
              <div className="feature-check">
                <i className="fas fa-check-circle"></i>
                <span>自动生成爆款选题 无限量</span>
              </div>
              <div className="feature-check">
                <i className="fas fa-check-circle"></i>
                <span>多模态纯视频理解和拆片</span>
                <span className="soon-badge">即将上线</span>
              </div>
            </div>
            
            <Card.Actions className="justify-center mt-auto">
              <Button 
                className="w-full bg-black text-white hover:bg-gray-800" 
                onClick={handlePayment}
                loading={orderLoading}
              >
                <i className="fab fa-weixin mr-1"></i>
                <i className="fab fa-alipay mr-2"></i>
                立即升级
              </Button>
            </Card.Actions>
          </Card.Body>
        </Card>
      </div>
      
      {/* 退款政策 */}
      <Card className="bg-base-100 shadow-md mb-8">
        <Card.Body>
          <Card.Title tag="h2" className="text-xl mb-4">支持7天无理由退款和开票</Card.Title>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="flex items-start">
              <i className="fas fa-check-circle text-success mt-1 mr-2"></i>
              <span>没学会退款</span>
            </div>
            <div className="flex items-start">
              <i className="fas fa-check-circle text-success mt-1 mr-2"></i>
              <span>效果不满意退款</span>
            </div>
            <div className="flex items-start">
              <i className="fas fa-check-circle text-success mt-1 mr-2"></i>
              <span>心情不好退款</span>
            </div>
            <div className="flex items-start">
              <i className="fas fa-check-circle text-success mt-1 mr-2"></i>
              <span>支持开发票</span>
            </div>
            <div className="flex items-start">
              <i className="fas fa-check-circle text-success mt-1 mr-2"></i>
              <span>支持对公转账</span>
            </div>
          </div>
        </Card.Body>
      </Card>
      
      {/* 支付模态框 */}
      <dialog id="payment_modal" className="modal" ref={paymentModalRef}>
        <div className="modal-box p-0 w-[450px]">
          {/* 头部 */}
          <div className="bg-black text-white p-4 text-center">
            <h3 className="text-lg font-bold">升级付费版</h3>
          </div>
          
          <div className="p-6">
            {/* 金额和二维码 */}
            <div className="flex flex-col items-center">
              <div className="flex flex-col items-center mb-6">
                <p className="text-sm opacity-70 mb-1">扫一扫付款（元）</p>
                <p className="text-4xl font-bold">{orderData?.amount || (loading ? '...' : priceConfig.paymentPrice)}</p>
              </div>
              
              {/* 二维码区域 */}
              {orderLoading ? (
                <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-sm mb-6 w-52 h-52 flex items-center justify-center">
                  <span className="loading loading-spinner loading-lg"></span>
                </div>
              ) : countdown === "0:00" ? (
                <div 
                  className="bg-white p-4 border border-gray-200 rounded-lg shadow-sm mb-6 cursor-pointer w-52 h-52 flex flex-col items-center justify-center"
                  onClick={handleRefreshQRCode}
                >
                  <i className="fas fa-qrcode text-gray-300 text-5xl mb-3"></i>
                  <p className="text-gray-500 text-sm text-center">二维码已过期<br />点击刷新</p>
                </div>
              ) : (
                <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-sm mb-6 cursor-pointer relative" onClick={handleShowPaymentSuccess}>
                  <img 
                    src={orderData?.payment_qr || "data:image/png;base64,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"} 
                    alt="支付二维码" 
                    className="w-52 h-52" 
                  />
                </div>
              )}
            </div>
            
            {/* 订单信息 */}
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <div className="flex justify-between mb-3">
                <span className="text-sm font-medium">订单号</span>
                <span className="text-sm">{orderData?.order_no || ""}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium">商品名称</span>
                <span className="text-sm">{orderData?.subject || "起号助手付费版年度订阅"}</span>
              </div>
            </div>
            
            {/* 支付方式 */}
            <div className="flex flex-col items-center mb-5">
              <p className="text-sm font-medium mb-3">支持以下应用扫码支付</p>
              <div className="flex justify-center gap-6">
                <div className="flex flex-col items-center">
                  <div className="bg-green-500 w-12 h-12 rounded-full flex items-center justify-center">
                    <i className="fab fa-weixin text-white text-2xl"></i>
                  </div>
                  <span className="text-xs mt-1">微信</span>
                </div>
                <div className="flex flex-col items-center">
                  <div className="bg-blue-500 w-12 h-12 rounded-full flex items-center justify-center">
                    <i className="fab fa-alipay text-white text-2xl"></i>
                  </div>
                  <span className="text-xs mt-1">支付宝</span>
                </div>
              </div>
            </div>
            
            {/* 倒计时 */}
            <div className="border-t border-gray-200 pt-4 text-center">
              <div className="inline-flex items-center px-2.5 py-1 rounded-full bg-gray-100">
                <i className="far fa-clock mr-1.5 text-gray-500"></i>
                <span className="text-sm font-medium">
                  {countdown === "0:00" ? (
                    <span className="text-red-500 font-bold">二维码已过期</span>
                  ) : (
                    <>二维码剩余 <span className="text-red-500 font-bold">{countdown}</span> 过期</>
                  )}
                </span>
              </div>
            </div>
          </div>
        </div>
        <form method="dialog" className="modal-backdrop">
          <button onClick={handleCloseModal}>close</button>
        </form>
      </dialog>

      {/* 支付成功模态框 */}
      <dialog id="payment_success_modal" className="modal" ref={successModalRef}>
        <div className="modal-box p-0 w-[450px]">
          {/* 头部 */}
          <div className="p-6 flex flex-col items-center">
            <div className="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mb-6 animate-pulse">
              <i className="fas fa-check text-green-500 text-5xl"></i>
            </div>
            <h3 className="text-2xl font-bold mb-2">支付成功</h3>
            <p className="text-3xl font-bold mb-6">¥{orderData?.amount || (loading ? '...' : priceConfig.paymentPrice)}</p>
            
            {/* 订单信息 */}
            <div className="bg-gray-50 rounded-lg p-4 w-full">
              <div className="flex justify-between">
                <span className="text-sm font-medium">订单号</span>
                <span className="text-sm">{orderData?.order_no || "**********"}</span>
              </div>
            </div>
          </div>
        </div>
        <form method="dialog" className="modal-backdrop">
          <button onClick={handleSuccessModalClose}>close</button>
        </form>
      </dialog>
    </div>
  );
};

export default Price; 
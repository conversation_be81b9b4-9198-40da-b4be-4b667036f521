import React, { useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ROUTES } from '../routes/constants';
import { isWeChatBrowser, wechatOffiaccountLogin } from '@/utils/wechat';
import { getAppConfig } from '@/api/common';
import { calcRedirectUri } from '@/utils/login';

// 定义 WxLogin 接口
interface WxLoginOptions {
  self_redirect?: boolean;
  id: string;
  appid: string;
  scope: string;
  redirect_uri: string;
  state?: string;
  style?: string;
  href?: string;
  onReady?: (isReady: boolean) => void;
}

// 定义 AppConfig 接口
interface AppConfig {
  wechat: {
    offiaccount: {
      appid: string;
    };
    openplatform: {
      appid: string;
    };
  };
}

// 扩展 Window 接口添加 WxLogin
declare global {
  interface Window {
    WxLogin: new (options: WxLoginOptions) => unknown;
  }
}

const Login: React.FC = () => {
  const location = useLocation();

  // Fetch the app configuration
  const fetchAppConfig = async () => {
    try {
      const response = await getAppConfig();
      if (response.data.code === 0 && response.data.data) {
        return response.data.data;
      }
    } catch (error) {
      console.error('Failed to fetch app config:', error);
    }
  };

  const init = async () => {
    const config = await fetchAppConfig();
    if (!isWeChatBrowser()) {
      initWechatQRLogin(config)
      return
    }

    // 微信内浏览器
    const offiaccountAppId = config?.wechat.offiaccount.appid || import.meta.env.VITE_WECHAT_OFFIACCOUNT_APP_ID;
    wechatOffiaccountLogin({
      appid: offiaccountAppId,
      redirect_uri: calcRedirectUri(ROUTES.WX_OFFIACCOUNT_LOGIN, location.search),
    })
  }

  useEffect(() => {
    init()
  }, []);

  const initWechatQRLogin = (appConfig: AppConfig | null = null) => {
    const openplatformAppId = appConfig?.wechat.openplatform.appid;
    
    if (!openplatformAppId) {
      console.error('初始化失败')
      return
    }

    new window.WxLogin({
      id: "login_container",
      appid: openplatformAppId,
      scope: "snsapi_login",
      redirect_uri: encodeURI(calcRedirectUri(ROUTES.WX_LOGIN, location.search)),
    });
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
        <div className="flex flex-col items-center justify-center py-4">
          <div id="login_container"></div>
          <div className="text-center mt-4 text-[13px] text-gray-600 font-sans">
            登录即代表同意
            <Link target='_blank' className="underline text-black hover:text-gray-600 mx-[5px]" to={ROUTES.USER_AGREEMENT}>用户协议</Link>
            和
            <Link target='_blank' className="underline text-black hover:text-gray-600 mx-[5px]" to={ROUTES.PRIVACY_POLICY}>隐私政策</Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login; 
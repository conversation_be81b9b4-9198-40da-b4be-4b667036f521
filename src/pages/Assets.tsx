import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Jo<PERSON>, <PERSON><PERSON> } from 'react-daisyui';
import Markdown from 'react-markdown';
import { AssetsOperation } from '@/components/AssetsOperation';
import { CustomPagination } from '@/components/CustomPagination';
import { createForm } from '@formily/core';
import { getAssetList } from '@/api/common';
import { commonResponsePipeline, formParamsPipeline } from '@/hooks/drawer/transformers';
import { createDrawer } from '@/hooks/create-drawer';
import { usePagination } from '@/hooks/use-pagination';
import { Field, FormProvider } from '@formily/react';
import { FormItem } from '@/components/formily/FormItem';
import { Input } from '@/components/formily/Input';
import { APPROVAL_STATUS, ASSET_EINO_NODE_NAMES, ASSET_PROPERTIY, ASSET_SOURCE, HANDLE_STATUS } from '@/cons';
import AssetsTable from '@/components/AssetsTable'
import clsx from 'clsx';
import {
  ColumnDef,
} from '@tanstack/react-table'
import { splitViralElements } from '@/utils/biz/common';
import { showTime, formatInteractionCount, formatCreateTime } from '@/utils';
import api from '@/api';
import { toast } from 'react-toastify';
import { Textarea } from '@/components/formily/Textarea';
import remarkGfm from 'remark-gfm'
import { useUserStore } from '@/models/user';
import AssetDetailDrawer from '@/components/AssetDetailDrawer';
import TopicTable from '@/components/TopicTable';
import { createPortal } from 'react-dom';

interface HandleStatusItem {
  label: string;
  // value: HANDLE_STATUS;
  color?: string;
  ping: boolean;
}
// const handleStatusList: HandleStatusItem[] = [{
//   label: '预处理',
//   value: HANDLE_STATUS.PREPROCESSING,
//   color: 'ghost',
//   ping: true
// }, {
//   label: '待处理',
//   value: HANDLE_STATUS.PENDING,
//   color: 'ghost',
//   ping: true
// }, {
//   label: '进行中',
//   value: HANDLE_STATUS.DOING,
//   color: 'warning',
//   ping: true
// }, {
//   label: '成功',
//   value: HANDLE_STATUS.SUCCESS,
//   color: 'success',
//   ping: false
// }, {
//   label: '失败',
//   value: HANDLE_STATUS.FAILED,
//   color: 'error',
//   ping: false
// }
// ]
// const handleStatusMap = handleStatusList.reduce((acc, item) => {
//   acc[item.value] = item
//   return acc
// }, {} as Record<string, HandleStatusItem>)
interface SourceItem {
  label: string;
  value: ASSET_SOURCE;
}

const sourceList: SourceItem[] = [{
  label: '手动',
  value: ASSET_SOURCE.MANUAL,
}, {
  label: '收藏夹',
  value: ASSET_SOURCE.COLLECTION,
}]

const sourceMap = sourceList.reduce((acc, item) => {
  acc[item.value] = item
  return acc
}, {} as Record<string, SourceItem>)

const form = createForm({
  initialValues: {
    keyword: '',
  }
})

const regenerateForm = createForm({
  initialValues: {
    video_title: '',
    script_type: '',
    user_ask: '',
  }
})

const useDrawer = createDrawer(commonResponsePipeline(formParamsPipeline(getAssetList) as any), { defaultValue: { pagination: { pageSize: 20 } } });

const assetsMarkdownModalTitleMap = {
  [ASSET_PROPERTIY.ASR_TEXT]: '视频识别文本',
  [ASSET_PROPERTIY.ANALYSIS_RESULT]: '素材视频原文',
  [ASSET_PROPERTIY.VIDEO_CONTENT]: '素材分析结果',
  [ASSET_PROPERTIY.VIDEO_SUMMARY]: '素材分析结果',
}


const einoExecuteNodeNameMap: Record<string, HandleStatusItem> = {
  [ASSET_EINO_NODE_NAMES.CHECK_EXTRACT_INFO]: {
    label: '待处理',
    color: 'gray',
    ping: true
  },
  [ASSET_EINO_NODE_NAMES.UPLOAD_VIDEO]: {
    label: '素材分析中',
    color: 'warning',
    ping: true
  },
  [ASSET_EINO_NODE_NAMES.UPLOAD_VIDEO_CALLBACK]: {
    label: '素材分析中',
    color: 'warning',
    ping: true
  },
  [ASSET_EINO_NODE_NAMES.VIDEO_PARSE]: {
    label: '素材分析中',
    color: 'warning',
    ping: true
  },
  [ASSET_EINO_NODE_NAMES.VIDEO_SNAPSHOTS]: {
    label: '素材分析中',
    color: 'warning',
    ping: true
  },
  [ASSET_EINO_NODE_NAMES.SUB_VIDEO_PROCESS]: {
    label: '素材分析中',
    color: 'warning',
    ping: true
  },
  [ASSET_EINO_NODE_NAMES.VIDEO_PROCESS_CALLBACK]: {
    label: '素材分析中',
    color: 'warning',
    ping: true
  },
  [ASSET_EINO_NODE_NAMES.PROCESS_REUSE_EXTRACT_DATA]: {
    label: '素材分析中',
    color: 'warning',
    ping: true
  },
  [ASSET_EINO_NODE_NAMES.COZE_ANALYSIS]: {
    label: '素材拆片中',
    color: 'info',
    ping: true
  },
  [ASSET_EINO_NODE_NAMES.COZE_ANALYSIS_CALLBACK]: {
    label: '素材拆片中',
    color: 'info',
    ping: true
  },
  [ASSET_EINO_NODE_NAMES.COZE_GENERATION]: {
    label: '选题生成中',
    color: 'success',
    ping: true
  },
  [ASSET_EINO_NODE_NAMES.COZE_GENERATION_CALLBACK]: {
    label: '选题生成中',
    color: 'success',
    ping: true
  },
}

const Assets: React.FC = () => {
  const [showAssetKey, setShowAssetKey] = useState<ASSET_PROPERTIY>();
  const { drawer, toolkit } = useDrawer();
  const pagination = usePagination(drawer, toolkit);
  const assetMarkdownRef = useRef<HTMLDialogElement>(null);
  const regenerateRef = useRef<HTMLDialogElement>(null);
  const confirmRef = useRef<HTMLDialogElement>(null);
  const revokeRef = useRef<HTMLDialogElement>(null);
  const [asset, setAsset] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const { actions: userActions } = useUserStore();
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [assetsIndex, setAssetsIndex] = useState(-1);
  const [pickedTopicInfo, setPickedTopicInfo] = useState<any>(null);

  const handleShowVideoProperty = (key: string, assetItem: any) => {
    setAsset(assetItem)
    setShowAssetKey(key as ASSET_PROPERTIY)
    assetMarkdownRef.current?.querySelector('.modal-box')?.scroll(0, 0)
    assetMarkdownRef.current?.showModal()
  }

  const handleOperateTopic = async () => {
    const {
      topic,
      topicIndex,
      targetStatus
    } = pickedTopicInfo
    const response = targetStatus == APPROVAL_STATUS.APPROVED ?
      await api.post('/client/topics/generationScriptCopy', { uuid: topic.uuid }) :
      await api.post('/client/topics/updateStatus', { uuid: topic.uuid, approval_status: targetStatus });
    if (response.data.code === 0) {
      const copied = [...drawer.list]
      const copiedtopicList = [...copied[assetsIndex].topicList]
      copiedtopicList.splice(topicIndex, 1, { ...topic, approval_status: targetStatus })
      const newAsset = { ...copied[assetsIndex], topicList: copiedtopicList }
      setAsset(newAsset)
      copied.splice(assetsIndex, 1, newAsset)
      toolkit.setList(copied)
      userActions.getDataStats()
      toast.success('更新成功')
    }
  }

  const showTopicModal = async (topic: any, topicIndex: number, targetStatus: APPROVAL_STATUS) => {
    setPickedTopicInfo({
      topic,
      topicIndex,
      targetStatus,
    })
    targetStatus == APPROVAL_STATUS.APPROVED ?
      confirmRef.current?.showModal() :
      revokeRef.current?.showModal()
  }
  const handleShowRegenerate = (assetItem: any) => {
    setAsset(assetItem)
    regenerateForm.setValues({ video_title: assetItem.video_title, script_type: assetItem.script_type, user_ask: '' })
    regenerateRef.current?.showModal()
  }
  const handleSubmit = async () => {
    const values = regenerateForm.values

    setLoading(true)
    try {
      const response = await api.post('/client/assets/regenerateTopic', {
        uuid: asset.uuid,
        user_ask: values.user_ask,
        script_type: values.script_type,
      })
      if (response.data.code === 0) {
        toast.success('重新生成中')
        setAsset({ ...asset, eino_execute_record: { ...asset.eino_execute_record, handle_status: HANDLE_STATUS.PENDING } })
        regenerateRef.current?.close()
        toolkit.refresh()
      }
    } finally {
      setLoading(false)
    }
  }

  const handleShowDetail = (assetItem: any, index: number) => {
    if (assetItem?.eino_execute_record && assetItem?.eino_execute_record?.handle_status != HANDLE_STATUS.SUCCESS && assetItem?.eino_execute_record?.source_type == 'ASSET') {
      let matchNodeNameMap: HandleStatusItem = {
        label: '待处理',
        color: 'gray',
        ping: true
      }
      if (einoExecuteNodeNameMap[assetItem.eino_execute_record?.current_node_name]) {
        matchNodeNameMap = einoExecuteNodeNameMap[assetItem.eino_execute_record?.current_node_name];
      }
      toast.info(`${matchNodeNameMap.label}，请稍候...`)
      return
    }

    setAssetsIndex(index)
    setAsset(assetItem);
    setDrawerVisible(true);
  }

  const columns: ColumnDef<any>[] = [
    {
      header: '视频',
      size: 280,
      accessorKey: 'video_title',
      cell: ({ row }) => {
        const asset = row.original;
        return (
          <div className="flex gap-3 items-center w-full">
            {/* 视频封面 */}
            <div className="flex-shrink-0">
              <div className="w-10 h-[71px] bg-base-200 rounded overflow-hidden relative">
                <a
                  href={asset.douyin_aweme?.AwemeURL}
                  target="_blank"
                  className="block w-full h-full"
                  onClick={e => e.stopPropagation()}
                >
                  <div
                    className="group w-full h-full"
                  >
                    <img
                      src={asset.douyin_aweme?.CoverURL}
                      alt="视频封面"
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute top-0 left-0 w-full h-full flex justify-center items-center opacity-0 group-hover:opacity-100 transition-all">
                      <div className="absolute top-0 left-0 w-full h-full bg-black opacity-50"></div>
                      <i className="fa-solid fa-play text-white z-99"></i>
                    </div>
                  </div>
                </a>
              </div>
            </div>

            {/* 视频信息 */}
            <div className="flex-1 min-w-0">
              <a
                href={asset.douyin_aweme?.AwemeURL}
                target="_blank"
                className="block"
                onClick={e => e.stopPropagation()}
              >
                <div className="font-medium line-clamp-2 hover:underline" title={asset.douyin_aweme?.Title}>
                  {asset.douyin_aweme?.Title}
                </div>
              </a>

              <div className="flex items-start text-xs text-base-content opacity-60 flex-wrap">
                <span>{sourceMap[asset.source]?.label || '未知'}</span>
                <span className="mx-1">•</span>
                {asset.douyin_aweme?.CreateTime && <span className="video-publish-time">{formatCreateTime(asset.douyin_aweme?.CreateTime)}</span>}
                <span className="mx-1">•</span>

                {/* 互动数据 */}
                <div className="flex items-center gap-1">
                  {asset.douyin_aweme?.LikedCount && (
                    <div className="flex items-center gap-0.5 text-base-content">
                      <i className="fas fa-heart"></i>
                      <span>{formatInteractionCount(asset.douyin_aweme.LikedCount)}</span>
                    </div>
                  )}
                  {asset.douyin_aweme?.CommentCount && (
                    <div className="flex items-center gap-0.5 text-base-content">
                      <i className="fas fa-comment"></i>
                      <span>{formatInteractionCount(asset.douyin_aweme.CommentCount)}</span>
                    </div>
                  )}
                  {asset.douyin_aweme?.CollectedCount && (
                    <div className="flex items-center gap-0.5 text-base-content">
                      <i className="fas fa-star"></i>
                      <span>{formatInteractionCount(asset.douyin_aweme.CollectedCount)}</span>
                    </div>
                  )}
                  {asset.douyin_aweme?.ShareCount && (
                    <div className="flex items-center gap-0.5 text-base-content">
                      <i className="fas fa-share"></i>
                      <span>{formatInteractionCount(asset.douyin_aweme.ShareCount)}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )
      }
    },
    {
      header: '作者',
      size: 160,
      accessorKey: 'author',
      cell: ({ row }) => {
        const asset = row.original;
        return (
          <div className="author-container flex items-center gap-2">
            <a
              className="w-6"
              href={`https://www.douyin.com/user/${asset.douyin_aweme?.SecUID}`}
              target="_blank"
              onClick={e => e.stopPropagation()}
            >
              <img
                src={asset.douyin_aweme?.Avatar}
                alt="作者头像"
                className="author-avatar w-6 h-6 rounded-full object-cover"
              />
            </a>
            <a
              href={`https://www.douyin.com/user/${asset.douyin_aweme?.SecUID}`}
              target="_blank"
              className="hover:underline"
              onClick={e => e.stopPropagation()}
            >
              <span className="author-name text-sm truncate max-w-20">
                {asset.douyin_aweme?.Nickname}
              </span>
            </a>
          </div>
        )
      }
    },
    {
      header: '视频标签',
      size: 160,
      accessorKey: 'video_tags',
      cell: ({ getValue }) => {
        const elements = splitViralElements(getValue() as any);

        if (!elements.length) {
          return <span className="text-base-content opacity-40">-</span>;
        }

        return (
          <div className="tag-container flex flex-wrap gap-1">
            {elements.map((ele, index) => (
              <Badge key={index} className="bg-[#f3f4f6] color-[#4b5563] h-auto text-xs text-base-content rounded-full">
                {ele}
              </Badge>
            ))}
          </div>
        )
      }
    },
    {
      accessorKey: 'script_type',
      header: '脚本类型',
      size: 100,
      cell: ({ getValue }) => {
        return (
          <div className="line-clamp-2">
            {getValue() as string || '-'}
          </div>
        )
      }
    },
    {
      accessorKey: 'viral_elements',
      header: '爆款元素',
      size: 120,
      cell: ({ getValue }) => {
        const elements = splitViralElements(getValue() as any);
        if (!elements.length) {
          return <span className="text-base-content opacity-40">-</span>;
        }

        return (
          <div className="tag-container flex flex-wrap gap-1">
            {elements.map((ele, index) => (
              <span key={index} className="bg-[#f3f4f6] color-[#4b5563] text-xs py-1 px-2 text-base-content rounded-full">
                {ele}
              </span>
            ))}
          </div>
        )
      }
    },
    {
      accessorKey: 'createTime',
      header: '添加时间',
      size: 120,
      cell: ({ getValue }) => {
        return showTime(getValue() as number)
      }
    },
    {
      header: '操作',
      size: 120,
      cell: ({ row }) => {
        const asset = row.original;

        if (asset.eino_execute_record?.handle_status == HANDLE_STATUS.SUCCESS || !asset.eino_execute_record || asset.eino_execute_record?.source_type != 'ASSET') {
          return (
            <Button
              className="btn-sm btn-outline min-w-24"
              onClick={(e) => {
                e.stopPropagation();
                handleShowDetail(asset, row.index);
              }}
            >
              查看详情
            </Button>
          );
        }

        let matchNodeNameMap: HandleStatusItem = {
          label: '待处理',
          color: 'gray',
          ping: true
        }
        if (einoExecuteNodeNameMap[asset.eino_execute_record?.current_node_name]) {
          matchNodeNameMap = einoExecuteNodeNameMap[asset.eino_execute_record?.current_node_name];
        }

        return (
          <div className="flex items-center gap-1">
            <div className="inline-grid *:[grid-area:1/1]">
              <span className={clsx('status !shadow-none', `status-${matchNodeNameMap?.color}`, { 'animate-ping': matchNodeNameMap?.ping })}></span>
              <span className={clsx('status !shadow-none', `status-${matchNodeNameMap?.color}`)}></span>
            </div>
            <span>{matchNodeNameMap?.label}</span>
          </div>
        )
      }
    },
  ]

  useEffect(() => {
    toolkit.search()
  }, [])

  return (
    <div className="w-full">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">素材库</h1>
        <p className="text-base-content opacity-60">素材积累的厚度，决定创作的高度</p>
      </div>

      <div className="flex flex-col sm:flex-row justify-between gap-4 mb-6">
        <div className="flex flex-wrap gap-2 items-center">
          {/* <div className="flex flex-col xl:flex-row gap-4"> */}
          <AssetsOperation showDouyinSync onRefresh={() => toolkit.search()} />
          <div className="flex items-center gap-1">
            <span className="text-sm opacity-70">配置抖音 Cookie 后可以自动化抓取抖音收藏夹</span>
            <a target="_blank" href={import.meta.env.VITE_COOKIE_DOC_URL} className="btn btn-link btn-m px-1">去了解 →</a>
          </div>
        </div>

        <FormProvider form={form}>
          <form onSubmit={e => {
            e.preventDefault()
            toolkit.search(form.getState().values)
          }}>
            <div className="flex-shrink-0">
              <Join>
                <Field
                  name="keyword"
                  decorator={[FormItem]}
                  component={[Input, { bordered: true, className: 'w-64 md:w-80 lg:w-96', placeholder: '搜索素材标题或脚本类型' }]}
                />
                <Button type="submit">
                  <i className="fas fa-search"></i>
                </Button>
              </Join>
            </div>
          </form>
        </FormProvider>
      </div>

      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-3">所有素材</h2>
        <div className="overflow-x-auto rounded-box border border-gray-200">
          <table className={clsx("table table-zebra w-full animate-pulse", { '!hidden': !drawer.loading })}>
            {/* head */}
            <thead>
              <tr>
                <th className="w-32">
                  <div className="h-4 bg-base-300 rounded w-16"></div>
                </th>
                <th className="w-32">
                  <div className="h-4 bg-base-300 rounded w-24"></div>
                </th>
                <th className="w-16">
                  <div className="h-4 bg-base-300 rounded w-12"></div>
                </th>
                <th className="w-20">
                  <div className="h-4 bg-base-300 rounded w-16"></div>
                </th>
                <th className="w-20">
                  <div className="h-4 bg-base-300 rounded w-24"></div>
                </th>
                <th className="w-20">
                  <div className="h-4 bg-base-300 rounded w-16"></div>
                </th>
                <th className="w-32">
                  <div className="h-4 bg-base-300 rounded w-20"></div>
                </th>
                <th className="w-24">
                  <div className="h-4 bg-base-300 rounded w-24"></div>
                </th>
                <th className="w-24">
                  <div className="h-4 bg-base-300 rounded w-20"></div>
                </th>
                <th className="w-20">
                  <div className="h-4 bg-base-300 rounded w-12"></div>
                </th>
              </tr>
            </thead>
            <tbody>
              {/* 生成多行骨架屏 */}
              {Array(5).fill(0).map((_, index) => (
                <tr key={index} className="hover:bg-base-300">
                  <td className="truncate">
                    <div className="h-4 bg-base-300 rounded w-28"></div>
                  </td>
                  <td>
                    <div className="h-4 bg-base-300 rounded w-24"></div>
                  </td>
                  <td>
                    <div className="h-4 bg-base-300 rounded w-12"></div>
                  </td>
                  <td>
                    <div className="h-4 bg-base-300 rounded w-16"></div>
                  </td>
                  <td>
                    <div className="flex items-center gap-1">
                      <div className="h-3 w-3 bg-base-300 rounded-full"></div>
                      <div className="h-4 bg-base-300 rounded w-12"></div>
                    </div>
                  </td>
                  <td>
                    <div className="h-4 bg-base-300 rounded w-16"></div>
                  </td>
                  <td>
                    <div className="h-4 bg-base-300 rounded w-24"></div>
                  </td>
                  <td>
                    <div className="flex items-center gap-1">
                      <div className="h-3 w-3 bg-base-300 rounded-full"></div>
                      <div className="h-4 bg-base-300 rounded w-12"></div>
                    </div>
                  </td>
                  <td>
                    <div className="h-4 bg-base-300 rounded w-20"></div>
                  </td>
                  <td>
                    <div className="h-6 bg-base-300 rounded w-16"></div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          <AssetsTable
            className={clsx({ '!hidden': drawer.loading })}
            data={drawer.list}
            columns={columns}
            onRowClick={(row, index) => handleShowDetail(row, index)}
          />
        </div>
      </div>

      <div className="flex justify-center mb-6">
        <CustomPagination disabled={drawer.loading} {...pagination} />
      </div>

      <Modal backdrop className="w-full xl:w-11/12 xl:max-w-5xl " containerClasses="modal-bottom sm:modal-middle" ref={assetMarkdownRef}>
        <Modal.Header className="font-bold">{assetsMarkdownModalTitleMap[showAssetKey as ASSET_PROPERTIY]}</Modal.Header>
        <Modal.Body className="max-h-[50vh] overflow-y-auto">
          <article
            className="
            prose prose-neutral
            prose-code:whitespace-pre-wrap 
            prose-table:table prose-table:border-gray-200 prose-table:rounded prose-table:border prose-table:table-zebra prose-table:table-auto
            prose-table:w-full
            prose-h1:text-xl
            prose-h2:text-lg
            prose-h3:text-base
            prose-th:min-w-24 max-w-none"
          >
            <Markdown remarkPlugins={[remarkGfm]}>{asset?.[showAssetKey as ASSET_PROPERTIY]}</Markdown>
          </article>
        </Modal.Body>
        <Modal.Actions>
          <form method="dialog">
            <Button>关闭</Button>
          </form>
        </Modal.Actions>
      </Modal>

      <Modal backdrop className="w-128 max-w-5xl max-h-3/4" containerClasses="modal-bottom sm:modal-middle" ref={regenerateRef}>
        <FormProvider form={regenerateForm}>
          <Modal.Header className="font-bold">再次生成选题</Modal.Header>
          <Modal.Body className="max-h-[50vh] overflow-y-auto">
            <div className="mb-4">
              <div className="text-base font-medium mb-2">参考素材</div>
              <Field
                name="video_title"
                decorator={[FormItem]}
                component={[Input, { bordered: true, className: 'w-full', placeholder: '参考素材', disabled: true }]}
              />
            </div>
            <div className="mb-4">
              <div className="text-base font-medium mb-2">脚本类型</div>
              <Field
                name="script_type"
                decorator={[FormItem]}
                component={[Input, { bordered: true, className: 'w-full', placeholder: '脚本类型' }]}
              />
            </div>
            <div className="mb-4 p-1">
              <div className="text-base font-medium mb-2">用户要求（选填）</div>
              <Field
                name="user_ask"
                decorator={[FormItem]}
                component={[Textarea, { bordered: true, className: 'w-full h-24', placeholder: '例如：标题中包含 xxx，加入 xx 元素等' }]}
              />
            </div>
          </Modal.Body>
          <Modal.Actions>
            <Button loading={loading} disabled={loading} onClick={() => regenerateRef.current?.close()}>取消</Button>
            <Button color="neutral" loading={loading} disabled={loading} onClick={handleSubmit}>确认再次生成</Button>
          </Modal.Actions>
        </FormProvider>
      </Modal>

      <Modal backdrop containerClasses="modal-bottom sm:modal-middle" ref={confirmRef}>
        <Modal.Header className="font-bold">确认选题</Modal.Header>
        <Modal.Body>
          确认选题后将添加到选题库中，同时开始生成文案
        </Modal.Body>
        <Modal.Actions>
          <Button onClick={() => confirmRef.current?.close()}>取消</Button>
          <form method="dialog">
            <Button color="neutral" onClick={() => handleOperateTopic()}>确认</Button>
          </form>
        </Modal.Actions>
      </Modal>

      <Modal backdrop containerClasses="modal-bottom sm:modal-middle" ref={revokeRef}>
        <Modal.Header className="font-bold">撤销选题</Modal.Header>
        <Modal.Body>
          确认要撤销这个选题吗？撤销后将从选题库中移除
        </Modal.Body>
        <Modal.Actions>
          <Button onClick={() => revokeRef.current?.close()}>取消</Button>
          <form method="dialog">
            <Button className="text-white" color="error" onClick={() => handleOperateTopic()}>确认撤销</Button>
          </form>
        </Modal.Actions>
      </Modal>

      {createPortal(
        <AssetDetailDrawer
          asset={asset}
          visible={drawerVisible}
          onClose={() => setDrawerVisible(false)}
          topicNode={<TopicTable embeded data={asset?.topicList || []} onOperation={showTopicModal} />}
          onRegenerate={() => handleShowRegenerate(asset)}
          onVideoPropertyClick={(property: string) => handleShowVideoProperty(property, asset)}
        />, document.body
      )}
    </div>
  );
};

export default Assets;
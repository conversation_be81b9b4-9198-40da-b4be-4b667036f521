import api from '@/api';
import { getTopicList } from '@/api/common';
import { CustomPagination } from '@/components/CustomPagination';
import { FormItem } from '@/components/formily/FormItem';
import { Input } from '@/components/formily/Input';
import { Textarea } from '@/components/formily/Textarea';
import { APPROVAL_STATUS, HANDLE_STATUS } from '@/cons';
import { createDrawer } from '@/hooks/create-drawer';
import { commonResponsePipeline, formParamsPipeline } from '@/hooks/drawer/transformers';
import { usePagination } from '@/hooks/use-pagination';
import { useAssetsStore } from '@/models/assets';
import { useUserStore } from '@/models/user';
import { showTime } from '@/utils';
import { splitViralElements } from '@/utils/biz/common';
import { createForm } from '@formily/core';
import { Field, FormConsumer, FormProvider } from '@formily/react';
import clsx from 'clsx';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, But<PERSON>, Join, Modal, Table } from 'react-daisyui';
import Markdown from 'react-markdown'
import { toast } from 'react-toastify';
import remarkGfm from 'remark-gfm'

const searchForm = createForm({
  initialValues: {
    keyword: '',
  }
})


const regenerateForm = createForm({
  initialValues: {
    userAsk: '',
  }
})

const useDrawer = createDrawer(commonResponsePipeline(formParamsPipeline(getTopicList) as any), { defaultValue: { pagination: { pageSize: 20 } } });

const Topics: React.FC = () => {
  const { drawer, toolkit } = useDrawer();
  const pagination = usePagination(drawer, toolkit);
  const { actions } = useAssetsStore();
  const { actions: userActions } = useUserStore();
  const ref = useRef<HTMLDialogElement>(null);
  const rejectRef = useRef<HTMLDialogElement>(null);
  const scriptCopyRef = useRef<HTMLDialogElement>(null);
  const regenerateRef = useRef<HTMLDialogElement>(null);
  const [topic, setTopic] = useState<any>({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    toolkit.search()
    actions.getUnfinishedStats()
  }, []);

  const handleShowTopic = (currentTopic: any) => {
    setTopic(currentTopic)
    ref.current?.showModal()
  }

  const handleShowScriptCopy = (currentTopic: any) => {
    setTopic(currentTopic)
    scriptCopyRef.current?.showModal()
  }

  const handleShowReject = (currentTopic: any) => {
    setTopic(currentTopic)
    rejectRef.current?.showModal()
  }

  const handleReject = useCallback(async () => {
    const response = await api.post('/client/topics/updateStatus', { uuid: topic.uuid, approval_status: APPROVAL_STATUS.PENDING });
    if (response.data.code === 0) {
      userActions.getDataStats()
      toast.success('取消成功')

    }
    toolkit.refresh();
  }, [topic])

  const showRegerateScriptCopy = () => {
    scriptCopyRef.current?.close()
    regenerateRef.current?.showModal()
    regenerateForm.reset()
  }

  const handleRegenerateSubmit = async () => {
    const values = regenerateForm.values
    setLoading(true)

    try {
      const response = await api.post('/client/topics/regenerationScriptCopy', {
        uuid: topic.uuid,
        user_ask: values.userAsk
      })
      if (response.data.code === 0) {
        toast.success('重新生成成功')
        regenerateRef.current?.close()
        toolkit.refresh()
      }
    } finally {
      setLoading(false)
    }
  }

  return (
    <div>
      <header className="mb-6">
        <h1 className="text-2xl font-bold">选题库</h1>
        <p className="text-base-content opacity-60">从素材库中精选并确认的优质选题集合</p>
      </header>

      <div className="flex flex-col sm:flex-row justify-between gap-4 mb-6">
        <div className="flex flex-wrap gap-2 items-center">
          <div className="flex items-center gap-1">
            <span className="text-sm opacity-70">在素材库确认的选题会在这里展示</span>
          </div>
        </div>

        <FormProvider form={searchForm}>
          <form onSubmit={e => {
            e.preventDefault()
            toolkit.search(searchForm.getState().values)
          }}>
            <div className="flex-shrink-0">
              <Join>
                <Field
                  name="keyword"
                  decorator={[FormItem]}
                  component={[Input, { bordered: true, className: 'w-64 md:w-80 lg:w-96', placeholder: '搜索选题标题或脚本类型' }]}
                />
                <Button type="submit">
                  <i className="fas fa-search"></i>
                </Button>
              </Join>
            </div>
          </form>
        </FormProvider>
      </div>

      <div className="mb-6 flex-1">
        <h2 className="text-lg font-semibold mb-3">已确认的选题</h2>
        <div className="overflow-x-auto rounded-box border border-gray-200 bg-base-100 table-responsive">
          <table className={clsx("table w-full", { '!hidden': !drawer.loading })}>
            <thead>
              <tr>
                <th className="w-32"><div className="skeleton h-4 w-20"></div></th>
                <th className="w-24"><div className="skeleton h-4 w-20"></div></th>
                <th className="w-24"><div className="skeleton h-4 w-16"></div></th>
                <th className="w-40"><div className="skeleton h-4 w-24"></div></th>
                <th className="w-20"><div className="skeleton h-4 w-12"></div></th>
              </tr>
            </thead>
            <tbody>
              {Array(5).fill(0).map((_, index) => (
                <tr key={index} className="hover:bg-base-300">
                  <td><div className="skeleton h-4 w-28"></div></td>
                  <td><div className="skeleton h-4 w-24"></div></td>
                  <td><div className="skeleton h-4 w-16"></div></td>
                  <td><div className="skeleton h-4 w-36"></div></td>
                  <td><div className="skeleton h-6 w-20"></div></td>
                </tr>
              ))}
            </tbody>
          </table>
          <Table className={clsx('w-full table-fixed', { '!hidden': drawer.loading })} zebra>
            <thead>
              <tr>
                <th className="w-48">选题标题</th>
                <th className="w-24">脚本类型</th>
                <th className="w-24">选题说明</th>
                <th className="w-24">爆款元素</th>
                <th className="w-40">参考素材</th>
                <th className="w-24">作者</th>
                <th className="w-24">添加时间</th>
                <th className="w-36">操作</th>
              </tr>
            </thead>

            <Table.Body>
              {drawer.list.map(topicItem => (
                <tr className="hover:bg-base-300" key={topicItem.uuid}>
                  <td title={topicItem.topic_title}>
                    <p className="font-bold">{topicItem.topic_title}</p>
                  </td>
                  <td>{topicItem.script_type}</td>
                  <td>
                    <a className="link link-hover" onClick={() => handleShowTopic(topicItem)}>查看</a>
                  </td>
                  <td>
                    <div className="tag-container flex flex-wrap gap-1">
                      {splitViralElements(topicItem.viral_elements).map((ele, index) => (
                        <Badge size="sm" key={index} className="h-auto bg-gray-200 text-gray-700 border-none mr-1">{ele}</Badge>
                      ))}
                    </div>
                  </td>
                  <td className="truncate" title={topicItem?.douyin_aweme?.Title}>
                    <a target="_blank" href={topicItem?.douyin_aweme?.AwemeURL} className="link link-hover">{topicItem?.douyin_aweme?.Title}</a>
                  </td>
                  <td>
                    <div className="flex items-center gap-2">
                      <div className="avatar">
                        <div className="w-6 h-6 rounded-full">
                          <a className="link link-hover" href={`https://www.douyin.com/user/${topicItem?.douyin_aweme?.SecUID}`} target="_blank">
                            <img src={topicItem?.douyin_aweme?.Avatar} alt="作者头像" />
                          </a>
                        </div>
                      </div>
                      <a className="link link-hover" href={`https://www.douyin.com/user/${topicItem?.douyin_aweme?.SecUID}`} target="_blank">
                        <span className="text-sm">{topicItem?.douyin_aweme?.Nickname}</span>
                      </a>
                    </div>
                  </td>
                  <td>{showTime(topicItem.create_time)}</td>
                  <td>
                    {{
                      [HANDLE_STATUS.SUCCESS]: (
                        <div className="flex gap-2">
                          <Button className="btn-outline btn-sm min-w-28" onClick={() => handleShowScriptCopy(topicItem)}>查看文案</Button>
                          <Button className="btn-outline btn-sm" onClick={() => handleShowReject(topicItem)}>
                            <i className="fas fa-trash"></i>
                          </Button>
                        </div>
                      ),
                      [HANDLE_STATUS.FAILED]: (
                        <div className="flex gap-2">
                          <Button disabled className="rounded bg-[#EBEBEB] text-[#464646] text-sm gap-2 min-w-28" size="sm">
                            文案处理失败
                          </Button>
                          <Button className="btn-outline btn-sm" onClick={() => handleShowReject(topicItem)}>
                            <i className="fas fa-trash"></i>
                          </Button>
                        </div>
                      ),
                      [HANDLE_STATUS.PROCESSING]: (
                        <Button className="rounded bg-[#EBEBEB] text-[#464646] text-sm gap-2" size="sm">
                          <span className="loading loading-spinner text-[#464646] loading-xs"></span>
                          文案生成中
                        </Button>
                      ),
                      [HANDLE_STATUS.PENDING]: (
                        <div className="flex gap-2">
                          <Button disabled className="rounded bg-[#EBEBEB] text-[#464646] text-sm gap-2 min-w-28" size="sm">
                            暂无文案
                          </Button>
                          <Button className="btn-outline btn-sm" onClick={() => handleShowReject(topicItem)}>
                            <i className="fas fa-trash"></i>
                          </Button>
                        </div>
                      ),
                      DEFAULT: (
                        <div className="flex gap-2">
                          <Button disabled className="rounded bg-[#EBEBEB] text-[#464646] text-sm gap-2 min-w-28" size="sm">
                            暂无文案
                          </Button>
                          <Button className="btn-outline btn-sm" onClick={() => handleShowReject(topicItem)}>
                            <i className="fas fa-trash"></i>
                          </Button>
                        </div>
                      )
                    }[topicItem.eino_execute_record?.handle_status as string || 'DEFAULT']}
                  </td>
                </tr>
              ))}
            </Table.Body>
          </Table>
        </div>
      </div>

      <div className="flex justify-center mb-6">
        <CustomPagination disabled={drawer.loading} {...pagination} />
      </div>

      <Modal backdrop className="!max-w-3xl" containerClasses="modal-bottom sm:modal-middle" ref={ref}>
        <Modal.Header className="font-bold">选题说明</Modal.Header>
        <Modal.Body className="max-h-[50vh] overflow-y-auto">
          <article className="prose prose-code:whitespace-pre-wrap prose-table:table-fixed max-w-none">
            <Markdown remarkPlugins={[remarkGfm]}>{topic?.topic_description}</Markdown>
          </article>
        </Modal.Body>
        <Modal.Actions>
          <form method="dialog">
            <Button>关闭</Button>
          </form>
        </Modal.Actions>
      </Modal>

      <Modal backdrop className="!max-w-3xl" containerClasses="modal-bottom sm:modal-middle" ref={scriptCopyRef}>
        <Modal.Header className="font-bold">文案说明</Modal.Header>
        <Modal.Body className="max-h-[50vh] overflow-y-auto">
          <article className="prose prose-code:whitespace-pre-wrap prose-table:table-fixed max-w-none">
            <Markdown remarkPlugins={[remarkGfm]}>{topic?.script_copy}</Markdown>
          </article>
        </Modal.Body>
        <Modal.Actions>
          <form method="dialog">
            <Button>关闭</Button>
          </form>
          <Button color="neutral" onClick={showRegerateScriptCopy}>修改文案</Button>
        </Modal.Actions>
      </Modal>

      <Modal backdrop containerClasses="modal-bottom sm:modal-middle" ref={rejectRef}>
        <Modal.Header className="font-bold">取消确认</Modal.Header>
        <Modal.Body>
          取消确认后选题不再选题库内展示，确认取消吗？
        </Modal.Body>
        <Modal.Actions>
          <Button onClick={() => rejectRef.current?.close()}>取消</Button>
          <form method="dialog">
            <Button className="text-white" color="error" onClick={() => handleReject()}>确认</Button>
          </form>
        </Modal.Actions>
      </Modal>

      <Modal backdrop className="!max-w-3xl" containerClasses="modal-bottom sm:modal-middle" ref={regenerateRef}>
        <FormProvider form={regenerateForm}>
          <Modal.Header className="font-bold">修改文案</Modal.Header>
          <Modal.Body className="max-h-[50vh] overflow-y-auto">
            <p className="text-sm opacity-75 mb-3">请在下方输入修改文案的要求，我们会根据您的需求进行调整</p>
            <div className="mb-4 p-1">
              <Field
                name="userAsk"
                required
                decorator={[FormItem]}
                component={[Textarea, { bordered: true, className: 'w-full h-24', placeholder: '例如：希望文案更加口语化，增加更多生活例子，简化专业术语等' }]}
              />
            </div>
          </Modal.Body>
          <Modal.Actions>
            <Button loading={loading} disabled={loading} onClick={() => regenerateRef.current?.close()}>取消</Button>
            <FormConsumer>
              {(form) => (
                <Button color="neutral" loading={loading} disabled={!form.valid || !form.modified || loading} onClick={handleRegenerateSubmit}>提交</Button>
              )}
            </FormConsumer>
          </Modal.Actions>
        </FormProvider>
      </Modal>
    </div>
  );
};

export default Topics;
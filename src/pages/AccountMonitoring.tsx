import React, { useState, useRef, useEffect } from 'react';
import { Button, Card, Modal } from 'react-daisyui';
import { toast } from 'react-toastify';
import { formatDateTime, formatCreateTimeCompatDetailed } from '@/utils';
import {
  trendInsightApi,
} from '../api/generated/client';

type TrendInsightAuthorSchema = components['schemas']['TrendInsightAuthorSchema'];

import type { components } from '../api/generated/types';

type UserAuthorKeywordInfo = components['schemas']['UserAuthorKeywordInfo'];



const AccountMonitoring: React.FC = () => {
  const [accounts, setAccounts] = useState<UserAuthorKeywordInfo[]>([]);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [searchResults, setSearchResults] = useState<TrendInsightAuthorSchema[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [loadError, setLoadError] = useState<string | null>(null);
  const [accountToDelete, setAccountToDelete] = useState<UserAuthorKeywordInfo | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isAddingAccount, setIsAddingAccount] = useState(false);
  const [authorStats, setAuthorStats] = useState({
    total: 0,
    active: 0,
    todayTotal: 0,
    totalVideos: 0
  });


  const [currentAccount, setCurrentAccount] = useState<UserAuthorKeywordInfo | null>(null);
  const [videos, setVideos] = useState<components['schemas']['UserInboxVideoRelatedWithAweme'][]>([]); // 建议使用更具体的类型
  const [videoLoading, setVideoLoading] = useState(false);

  const addAccountModalRef = useRef<HTMLDialogElement>(null);
  const batchSyncModalRef = useRef<HTMLDialogElement>(null);
  const accountVideosModalRef = useRef<HTMLDialogElement>(null);

  const deleteConfirmModalRef = useRef<HTMLDialogElement>(null);

  // 获取账号列表
  const fetchAccounts = async () => {
    try {
      setIsLoading(true);
      setLoadError(null);

      // 使用新的 getUserAuthorKeywords API 来获取账号监控数据
      const response = await trendInsightApi.getUserAuthorKeywords({
        page: 1,
        page_size: 50
      });

      if (response.code === 0) {
        if (response.data?.keywords) {
          // 直接使用后端返回的数据，无需转换
          setAccounts(response.data.keywords);
          setLoadError(null); // 清除之前的错误
        } else {
          // API成功但没有数据
          setAccounts([]);
          setLoadError(null);
        }
      } else {
        // API返回错误状态
        const errorMessage = response.msg || `API返回错误状态码: ${response.code}`;
        console.error('API返回异常:', response);
        setLoadError(errorMessage);
        toast.error(errorMessage);
      }
    } catch (error: unknown) {
      console.error('获取账号列表失败:', error);
      const errorMessage = error instanceof Error ? `获取数据失败: ${error.message}` : '获取数据失败';
      setLoadError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // 获取作者关键词统计信息
  const fetchAuthorStats = async () => {
    try {
      const response = await trendInsightApi.getAuthorKeywordStats();

      if (response.code === 0 && response.data) {
        setAuthorStats({
          total: response.data.keyword_count || 0,
          active: response.data.active_sources || 0,
          todayTotal: response.data.today_count || 0,
          totalVideos: response.data.total_authors || 0
        });
      } else {
        console.error('获取作者关键词统计失败:', response);
      }
    } catch (error: unknown) {
      console.error('获取作者关键词统计出错:', error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      toast.error(`获取统计数据失败: ${errorMessage}`);
    }
  };

  // 组件挂载时获取数据
  useEffect(() => {
    fetchAccounts();
    fetchAuthorStats();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const formatNumber = (num: number) => {
    if (num >= 10000) {
      return (num / 10000).toFixed(1) + '万';
    }
    return num.toString();
  };



  const handleAddAccount = () => {
    addAccountModalRef.current?.showModal();
  };

  const handleSearchCreator = async () => {
    if (!searchKeyword.trim()) {
      toast.error('请输入创作者关键词');
      return;
    }

    setIsSearching(true);
    setSearchError(null);

    try {
      // 调试：检查 API 客户端状态
      console.log('TrendInsight API 客户端状态:', {
        trendInsightApi: !!trendInsightApi,
        searchAuthors: <AUTHORS>
      });

      // 调用真实的 API
      const response = await trendInsightApi.searchAuthors({
        keyword: searchKeyword.trim(),
        type: 'author',
        total: 20,
      });

      if (response.code === 0 && response.data?.authors) {
        if (response.data.authors.length > 0) {
          setSearchResults(response.data.authors);
          setShowResults(true);
          toast.success(`找到 ${response.data.authors.length} 个相关创作者`);
        } else {
          // API 成功但没有找到相关创作者
          setSearchResults([]);
          setShowResults(true);
          toast.info('未找到相关创作者，请尝试其他关键词');
        }
      } else {
        // API 返回非 200 状态码或数据格式异常
        const errorMessage = response.msg || `API 返回错误状态码: ${response.code}`;
        setSearchError(errorMessage);
        toast.error(errorMessage);
      }
    } catch (error: unknown) {
      console.error('API 调用失败:', error);

      // 根据错误类型提供不同的错误信息和建议
      let errorMessage = '搜索失败';
      let suggestion = '';

      if (error instanceof Error) {
        if (error.name === 'NetworkError' || error.message.includes('fetch')) {
          errorMessage = '网络连接失败';
          suggestion = '请检查网络连接后重试';
        } else if (error.message.includes('timeout')) {
          errorMessage = '请求超时';
          suggestion = '服务器响应较慢，请稍后重试';
        } else {
          errorMessage = error.message;
          suggestion = '请稍后重试或联系技术支持';
        }
      } else {
        const errorObj = error as Record<string, unknown>;

        if (typeof errorObj.status === 'number') {
          if (errorObj.status === 401) {
            errorMessage = '认证失败';
            suggestion = '请检查登录状态';
          } else if (errorObj.status === 403) {
            errorMessage = '权限不足';
            suggestion = '请联系管理员获取权限';
          } else if (errorObj.status >= 500) {
            errorMessage = '服务器内部错误';
            suggestion = '服务器暂时不可用，请稍后重试';
          }
        } else {
          errorMessage = '未知错误';
          suggestion = '请稍后重试或联系技术支持';
        }
      }

      setSearchError(`${errorMessage}: ${suggestion}`);
      toast.error(`${errorMessage}，${suggestion}`);
    } finally {
      setIsSearching(false);
    }
  };

  const handleConfirmAddAccount = async (creator?: TrendInsightAuthorSchema) => {
    if (!searchKeyword.trim()) {
      toast.error('请输入创作者关键词');
      return;
    }

    try {
      setIsAddingAccount(true);

      // 使用新的 upsertAuthorKeywords API 来创建账号监控
      const response = await trendInsightApi.upsertAuthorKeywords({
        user_id: creator?.user_id || searchKeyword.trim()
      });

      if (response.code === 0) {
        toast.success(`创作者"${creator?.user_name || searchKeyword}"添加成功！`);
        // 重新获取账号列表
        await fetchAccounts();
        // 重新获取统计数据
        await fetchAuthorStats();
        resetAddAccountModal();
      } else {
        toast.error(`添加失败: ${response.msg || '未知错误'}`);
      }
    } catch (error: unknown) {
      console.error('添加账号失败:', error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      toast.error(`添加账号失败: ${errorMessage}`);
    } finally {
      setIsAddingAccount(false);
    }
  };

  const resetAddAccountModal = () => {
    setSearchKeyword('');
    setSearchResults([]);
    setShowResults(false);
    setSearchError(null);
    addAccountModalRef.current?.close();
  };

  const handleBatchSync = async () => {
    setIsSyncing(true);
    batchSyncModalRef.current?.close();

    try {
      // 重新获取最新的账号数据
      await fetchAccounts();

      // 重新获取最新的统计数据
      await fetchAuthorStats();

      // 这里可以添加更多的同步逻辑，比如：
      // 1. 检查每个账号的最新视频
      // 2. 更新统计数据
      // 3. 同步到灵感收件箱等

      toast.success('批量同步完成！数据已更新');
    } catch (error: unknown) {
      console.error('批量同步失败:', error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      toast.error(`同步失败: ${errorMessage}`);
    } finally {
      setIsSyncing(false);
    }
  };

  const handleDeleteAccountWithConfirm = (account: UserAuthorKeywordInfo) => {
    // 检查是否有有效的ID
    if (!account.id) {
      toast.error('账号ID无效，无法删除');
      return;
    }

    // 设置要删除的账号并显示确认模态框
    setAccountToDelete(account);
    deleteConfirmModalRef.current?.showModal();
  };

  const handleConfirmDelete = () => {
    if (accountToDelete?.id) {
      handleDeleteAccount(accountToDelete.id);
      setAccountToDelete(null);
      deleteConfirmModalRef.current?.close();
    }
  };

  const handleCancelDelete = () => {
    setAccountToDelete(null);
    deleteConfirmModalRef.current?.close();
  };

  const handleDeleteAccount = async (accountId: string) => {
    const account = accounts.find(a => a.id === accountId);
    if (!account) {
      toast.error('账号不存在');
      return;
    }

    try {
      setIsDeleting(true);

      // 使用现有的 deleteUserKeyword API 删除账号监控
      const response = await trendInsightApi.deleteUserKeyword(accountId);

      if (response.code === 0) {
        // 从本地状态中移除
        setAccounts(prev => prev.filter(a => a.id !== accountId));
        // 重新获取最新的统计数据
        await fetchAuthorStats();
        toast.success(`创作者"${account.trend_author?.user_name || account.display_name || '未知账号'}"监控已删除`);
      } else {
        toast.error(`删除失败: ${response.msg || '未知错误'}`);
      }
    } catch (error: unknown) {
      console.error('删除账号失败:', error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      toast.error(`删除账号失败: ${errorMessage}`);
    } finally {
      setIsDeleting(false);
    }
  };



  const viewAccountVideos = async (account: UserAuthorKeywordInfo) => {
    setCurrentAccount(account);
    setVideoLoading(true);
    accountVideosModalRef.current?.showModal(); // 先显示模态框，再加载数据
    try {
      // 假设 client 是已经实例化的 API client
      // 这里需要引入 trendInsightApi
      const response = await trendInsightApi.getAuthorVideos({
        trendinsight_user_id: account.trend_author?.user_id || '', // 使用 trend_author.user_id
        page: 1,
        page_size: 20 // 假设获取最新20个视频
      });
      if (response.code === 0 && response.data?.list) {
        setVideos(response.data.list);
      } else {
        setVideos([]);
        toast.error(response.msg || '获取视频列表失败');
      }
    } catch (error) {
      console.error('请求视频列表时发生错误:', error);
      toast.error('请求视频列表时发生错误');
      setVideos([]);
    } finally {
      setVideoLoading(false);
    }
  };



  // 我们直接使用从 API 获取的 authorStats 数据

  return (
    <div>
      <header className="mb-6">
        <h1 className="text-2xl font-bold">对标账号监控</h1>
        <p className="text-base-content opacity-60">监控竞品账号动态，获取最新灵感和素材</p>
      </header>

      {/* 操作区域 */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
        <Button className="bg-black text-white hover:bg-gray-800" onClick={handleAddAccount}>
          <i className="fas fa-plus mr-2"></i>添加账号
        </Button>
        <div className="flex items-center gap-3">
          <span className="text-sm opacity-60">
            最后更新：{accounts.length > 0 ?
              (accounts[0]?.updated_at ? formatDateTime(accounts[0].updated_at) : '未知') :
              '暂无数据'}
          </span>
          <Button
            size="sm"
            className="btn-outline"
            onClick={() => batchSyncModalRef.current?.showModal()}
            loading={isSyncing}
          >
            <i className="fas fa-sync mr-2"></i>批量同步
          </Button>
        </div>
      </div>

      {/* 错误提示 */}
      {loadError && (
        <div className="alert alert-error mb-6">
          <i className="fas fa-exclamation-triangle"></i>
          <div className="flex-1">
            <div>
              <div className="font-bold">数据加载失败</div>
              <div className="text-sm">{loadError}</div>
            </div>
          </div>
          <Button
            size="sm"
            className="btn-outline btn-sm"
            onClick={fetchAccounts}
            loading={isLoading}
            disabled={isLoading}
          >
            <i className="fas fa-redo mr-1"></i>重试
          </Button>
        </div>
      )}

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8 hidden lg:grid">
        <div className="stat bg-base-100 shadow rounded-box">
          <div className="stat-figure text-primary">
            <i className="fas fa-eye text-3xl"></i>
          </div>
          <div className="stat-title">监控账号</div>
          <div className="stat-value text-primary">{authorStats.total}</div>
          <div className="stat-desc">活跃监控中</div>
        </div>

        <div className="stat bg-base-100 shadow rounded-box">
          <div className="stat-figure text-secondary">
            <i className="fas fa-video text-3xl"></i>
          </div>
          <div className="stat-title">今日更新</div>
          <div className="stat-value text-secondary">{authorStats.todayTotal}</div>
          <div className="stat-desc">新视频</div>
        </div>

        <div className="stat bg-base-100 shadow rounded-box">
          <div className="stat-figure text-info">
            <i className="fas fa-clock text-3xl"></i>
          </div>
          <div className="stat-title">累计更新</div>
          <div className="stat-value text-info">{authorStats.totalVideos}</div>
          <div className="stat-desc">总视频数</div>
        </div>
      </div>

      {/* 监控账号列表 */}
      <Card className="bg-base-100 shadow-md">
        <Card.Body>
          <h3 className="text-lg font-bold mb-4">监控账号列表</h3>

          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="flex flex-col items-center gap-3">
                <span className="loading loading-spinner loading-lg"></span>
                <p className="text-sm opacity-60">正在加载监控账号...</p>
              </div>
            </div>
          ) : accounts.length === 0 && !loadError ? (
            <div className="text-center py-12">
              <i className="fas fa-users text-6xl text-base-300 mb-4"></i>
              <h4 className="text-lg font-medium mb-2">暂无监控账号</h4>
              <p className="text-base-content opacity-60 mb-6">
                添加您想要监控的创作者账号，获取最新的视频内容和创作灵感
              </p>
              <Button className="bg-black text-white hover:bg-gray-800" onClick={handleAddAccount}>
                <i className="fas fa-plus mr-2"></i>添加第一个监控账号
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {accounts.map(account => (
                <div key={account.id} className="border border-base-300 rounded-lg bg-base-100 p-4 transition-shadow duration-200 hover:shadow-md">
                  <div className="flex items-start gap-3 mb-3">
                    <img
                      src={account.trend_author?.user_head_logo || "https://p3-pc.douyinpic.com/img/tos-cn-avt-0015/oEgvwNOgAQABAADNFJOFwAA6OggeRgwVAA~c5_300x300.jpeg?from=**********"}
                      alt="账号头像"
                      className="w-12 h-12 rounded-full object-cover"
                    />
                    <div className="flex-1">
                      <h4 className="font-medium text-base">{account.trend_author?.user_name || account.display_name || '未知账号'}</h4>
                      <p className="text-sm opacity-60">@{account.trend_author?.user_name || account.display_name || '未知账号'}</p>
                      <div className="flex items-center mt-1">
                        <div className="w-2 h-2 rounded-full bg-success mr-1"></div>
                        <span className="text-xs text-success">监控中</span>
                      </div>
                    </div>
                    <div className="dropdown dropdown-end">
                      <div tabIndex={0} role="button" className="btn btn-ghost btn-xs">
                        <i className="fas fa-ellipsis-v"></i>
                      </div>
                      <ul tabIndex={0} className="dropdown-content menu bg-base-100 rounded-box z-[1] w-32 p-2 shadow">
                        <li>
                          <a onClick={() => handleDeleteAccountWithConfirm(account)}>
                            <i className="fas fa-trash mr-2"></i>删除
                          </a>
                        </li>
                      </ul>
                    </div>
                  </div>

                  <div className="grid grid-cols-3 gap-2 text-center mb-3">
                    <div>
                      <div className="text-lg font-semibold">{formatNumber(account.trend_author?.fans_count_int || 0)}</div>
                      <div className="text-xs opacity-60">粉丝</div>
                    </div>
                    <div>
                      <div className="text-lg font-semibold">{account.trend_author?.item_count_int || 0}</div>
                      <div className="text-xs opacity-60">作品</div>
                    </div>
                    <div>
                      <div className="text-lg font-semibold">0</div>
                      <div className="text-xs opacity-60">今日新增</div>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <button
                      className="btn btn-xs btn-outline flex-1"
                      onClick={() => viewAccountVideos(account)}
                    >
                      查看详情
                    </button>
                  </div>
                </div>
              ))}

              {/* 添加账号占位卡片 */}
              <div className="border-2 border-dashed border-base-300 rounded-lg p-4 flex items-center justify-center min-h-[200px]">
                <div className="text-center">
                  <i className="fas fa-plus text-2xl text-base-300 mb-2"></i>
                  <p className="text-sm opacity-60 mb-2">添加更多监控账号</p>
                  <Button size="sm" className="btn-outline" onClick={handleAddAccount}>
                    添加账号
                  </Button>
                </div>
              </div>
            </div>
          )}
        </Card.Body>
      </Card>

      {/* 添加账号模态框 */}
      <Modal ref={addAccountModalRef} containerClasses="modal-bottom sm:modal-middle" className="sm:max-w-2xl">
        <Modal.Header className="text-lg font-bold mb-6">添加监控账号</Modal.Header>
        <Modal.Body>
          {!showResults ? (
            <div className="space-y-6">
              <div className="form-control">
                <label className="label">
                  <span className="label-text">创作者关键词</span>
                </label>
                <div className="join w-full">
                  <input
                    type="text"
                    className="input input-bordered join-item flex-1"
                    placeholder="请输入抖音账号或昵称"
                    value={searchKeyword}
                    onChange={(e) => setSearchKeyword(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && handleSearchCreator()}
                    disabled={isSearching}
                  />
                  <Button
                    className="join-item bg-black text-white hover:bg-gray-800"
                    onClick={handleSearchCreator}
                    loading={isSearching}
                    disabled={isSearching}
                  >
                    <i className="fas fa-search mr-2"></i>
                    {isSearching ? '搜索中...' : '搜索'}
                  </Button>
                </div>
              </div>

              <div className="form-control">
                <label className="label">
                  <span className="label-text">平台</span>
                </label>
                <select className="select select-bordered w-full" disabled>
                  <option selected>抖音</option>
                </select>
              </div>

              {isSearching && (
                <div className="flex items-center justify-center py-8">
                  <div className="flex flex-col items-center gap-3">
                    <span className="loading loading-spinner loading-lg"></span>
                    <p className="text-sm opacity-60">正在搜索相关创作者...</p>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-6">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <h4 className="font-medium text-lg">搜索结果</h4>
                <Button size="sm" className="btn-outline" onClick={() => setShowResults(false)}>
                  <i className="fas fa-arrow-left mr-2"></i>重新搜索
                </Button>
              </div>

              {searchError && (
                <div className="alert alert-error">
                  <i className="fas fa-exclamation-triangle"></i>
                  <div className="flex-1">
                    <span>{searchError}</span>
                  </div>
                  <Button
                    size="sm"
                    className="btn-outline btn-sm"
                    onClick={handleSearchCreator}
                    loading={isSearching}
                    disabled={isSearching}
                  >
                    <i className="fas fa-redo mr-1"></i>重试
                  </Button>
                </div>
              )}

              {searchResults.length > 0 ? (
                <>
                  <div className="space-y-3 border border-base-300 rounded-lg p-4 max-h-80 overflow-y-auto">
                    {searchResults.map((creator, index) => (
                      <div key={creator.user_id || index} className="border border-base-300 rounded-lg p-4 hover:bg-base-50 transition-colors">
                        <div className="flex items-start gap-4">
                          <div className="avatar">
                            <div className="w-12 h-12 rounded-full">
                              <img
                                src={creator.user_head_logo || '/placeholder-avatar.jpg'}
                                alt={creator.user_name}
                                className="object-cover"
                              />
                            </div>
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-3">
                              <h5 className="font-medium text-base truncate">{creator.user_name}</h5>
                              <span className="text-sm opacity-60">@{creator.user_id}</span>
                            </div>
                            <div className="flex items-center gap-4 text-sm opacity-60">
                              <span>
                                <i className="fas fa-users mr-1"></i>
                                {formatNumber(typeof creator.follow_count === 'string' ? parseInt(creator.follow_count) || 0 : creator.follow_count || 0)}
                              </span>
                              <span>
                                <i className="fas fa-video mr-1"></i>
                                {creator.item_count || 0}作品
                              </span>
                              <span>
                                <i className="fas fa-heart mr-1"></i>
                                {formatNumber(typeof creator.like_count === 'string' ? parseInt(creator.like_count) || 0 : creator.like_count || 0)}获赞
                              </span>
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            <Button
                              size="sm"
                              className="bg-black text-white hover:bg-gray-800"
                              onClick={() => handleConfirmAddAccount(creator)}
                              disabled={isAddingAccount}
                              loading={isAddingAccount}
                            >
                              {isAddingAccount ? '添加中...' : '添加监控'}
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </>
              ) : !searchError && (
                <div className="text-center py-8">
                  <i className="fas fa-search text-4xl text-base-300 mb-4"></i>
                  <p className="text-base-content opacity-60 mb-4">未找到相关创作者</p>
                  <p className="text-sm text-base-content opacity-40 mb-4">
                    请尝试使用其他关键词或检查关键词拼写
                  </p>
                  <div className="flex gap-2 justify-center">
                    <Button
                      size="sm"
                      className="btn-outline"
                      onClick={() => setShowResults(false)}
                    >
                      <i className="fas fa-arrow-left mr-2"></i>重新搜索
                    </Button>
                    <Button
                      size="sm"
                      className="bg-black text-white hover:bg-gray-800"
                      onClick={() => handleConfirmAddAccount()}
                      disabled={isAddingAccount}
                      loading={isAddingAccount}
                    >
                      <i className="fas fa-plus mr-2"></i>{isAddingAccount ? '添加中...' : '仍然添加监控'}
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </Modal.Body>
      </Modal>

      {/* 账号视频列表模态框 */}
      <Modal ref={accountVideosModalRef} containerClasses="modal-bottom sm:modal-middle">
        <Modal.Body>
          {currentAccount && (
            <div className="flex items-center gap-4">
              <img src={currentAccount.trend_author?.user_head_logo || "/placeholder-avatar.jpg"} alt="账号头像" className="w-16 h-16 rounded-full" />
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <h4 className="text-lg font-medium">{currentAccount.trend_author?.user_name || currentAccount.display_name}</h4>
                  <span className="text-sm opacity-60">@{currentAccount.trend_author?.user_id || currentAccount.id}</span>
                </div>
                <div className="flex items-center gap-4 text-sm">
                  <span>{formatNumber(currentAccount.trend_author?.fans_count_int || 0)}粉丝</span>
                  <span>{currentAccount.trend_author?.item_count_int || 0}作品</span>
                  <span>{formatNumber(currentAccount.trend_author?.like_count_int || 0)}获赞</span>
                </div>
              </div>
            </div>
          )}

          <div className="divider"></div>

          <div>
            <h4 className="font-medium mb-2">最新视频</h4>
            <div className="overflow-x-auto max-h-80 overflow-y-auto">
              {videoLoading ? (
                <div className="flex justify-center items-center h-32">
                  <span className="loading loading-spinner loading-lg"></span>
                </div>
              ) : videos.length > 0 ? (
                <table className="table table-sm">
                  <thead>
                    <tr className="bg-base-200">
                      <th className="font-medium text-base-content w-3/5">标题</th>
                      <th className="font-medium text-base-content w-2/5 text-center">发布时间</th>
                    </tr>
                  </thead>
                  <tbody>
                    {videos.map((video, index) => (
                      <tr key={index} className="hover:bg-base-50">
                        <td className="font-medium text-base-content">{video.douyin_aweme?.Title}</td>
                        <td className="text-sm text-base-content opacity-70 text-center">
                          {formatCreateTimeCompatDetailed(video.douyin_aweme?.CreateTime)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              ) : (
                <div className="text-center py-4">暂无视频</div>
              )}
            </div>
          </div>
        </Modal.Body>
        <Modal.Actions>
          <form method="dialog">
            <Button>关闭</Button>
          </form>
        </Modal.Actions>
      </Modal>

      {/* 批量同步确认模态框 */}
      <Modal ref={batchSyncModalRef} containerClasses="modal-bottom sm:modal-middle">
        <Modal.Header className="text-lg font-bold mb-4">批量同步确认</Modal.Header>
        <Modal.Body>
          <div className="space-y-4">
            <p>确定要同步所有监控账号的最新数据吗？</p>
            <div className="bg-base-200 p-3 rounded-lg">
              <div className="text-sm opacity-80">
                <p>• 将检查所有账号的最新视频</p>
                <p>• 监控到的数据将自动导入至灵感收件箱</p>
                <p>• 更新账号监控数据</p>
                <p>• 预计需要 1-2 分钟完成</p>
              </div>
            </div>
          </div>
        </Modal.Body>
        <Modal.Actions>
          <form method="dialog" className="flex gap-2">
            <Button onClick={() => batchSyncModalRef.current?.close()}>取消</Button>
            <Button
              className="bg-black text-white hover:bg-gray-800"
              onClick={handleBatchSync}
              disabled={isSyncing}
              loading={isSyncing}
            >
              {isSyncing ? '同步中...' : '确认同步'}
            </Button>
          </form>
        </Modal.Actions>
      </Modal>

      {/* 删除确认模态框 */}
      <Modal ref={deleteConfirmModalRef} className="w-11/12 max-w-md">
        <h3 className="text-lg font-bold mb-4">确认删除</h3>
        <div className="space-y-4">
          <p>
            您确定要删除创作者 <span className="font-medium">{accountToDelete?.trend_author?.user_name || accountToDelete?.display_name || '未知创作者'}</span> 的监控吗？
          </p>
          <div className="bg-base-200 p-3 rounded-lg">
            <div className="text-sm opacity-80">
              <p>• 删除后将停止监控该创作者的更新</p>
              <p>• 已收集的历史数据将被保留</p>
              <p>• 此操作无法撤销</p>
            </div>
          </div>
        </div>
        <div className="modal-action">
          <div className="flex gap-2">
            <Button
              className="btn"
              onClick={handleCancelDelete}
            >
              取消
            </Button>
            <Button
              className="btn btn-error text-white"
              onClick={handleConfirmDelete}
              disabled={isDeleting}
              loading={isDeleting}
            >
              {isDeleting ? '删除中...' : '确认删除'}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default AccountMonitoring;

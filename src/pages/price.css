.pricing-card {
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.pricing-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}
.price-tag {
  font-size: 2.5rem;
  font-weight: 700;
}
.price-original {
  text-decoration: line-through;
  opacity: 0.6;
}
.feature-check, .feature-cross {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
}
.feature-check i {
  color: #10b981;
  margin-right: 0.75rem;
}
.feature-cross i {
  color: #ef4444;
  margin-right: 0.75rem;
}
.soon-badge {
  margin-left: 0.5rem;
  font-size: 0.7rem;
  background-color: #6366f1;
  color: white;
  padding: 0.15rem 0.5rem;
  border-radius: 1rem;
} 
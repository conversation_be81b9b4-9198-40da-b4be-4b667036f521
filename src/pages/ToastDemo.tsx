import React from 'react';
import { showToast } from '../utils/toast';
import ColorTest from '../components/ColorTest';

const ToastDemo: React.FC = () => {
    const handleSuccessToast = () => {
        showToast.success('🎉 操作成功！这是一个成功提示');
    };

    const handleErrorToast = () => {
        showToast.error('❌ 操作失败！这是一个错误提示');
    };

    const handleWarningToast = () => {
        showToast.warning('⚠️ 请注意！这是一个警告提示');
    };

    const handleInfoToast = () => {
        showToast.info('ℹ️ 温馨提示！这是一个信息提示');
    };

    const handleDefaultToast = () => {
        showToast.default('📝 这是一个默认提示');
    };

    const handleCustomToast = () => {
        showToast.custom(
            <div className="flex items-center">
                <div className="avatar placeholder mr-3">
                    <div className="bg-primary text-primary-content rounded-full w-8">
                        <span className="text-xs">🔥</span>
                    </div>
                </div>
                <div>
                    <div className="font-semibold">自定义内容</div>
                    <div className="text-sm opacity-75">这是一个带有自定义 JSX 内容的提示</div>
                </div>
            </div>,
            'info'
        );
    };

    const handlePromiseToast = () => {
        const promise = new Promise((resolve, reject) => {
            setTimeout(() => {
                if (Math.random() > 0.5) {
                    resolve('数据加载成功！');
                } else {
                    reject('数据加载失败！');
                }
            }, 2000);
        });

        showToast.promise(
            promise,
            {
                pending: '正在加载数据...',
                success: '数据加载成功！',
                error: '数据加载失败！'
            }
        );
    };

    const handleLongMessage = () => {
        showToast.info(
            '这是一个比较长的消息内容，用来测试 Toast 组件在处理长文本时的显示效果。这个消息会自动换行，并且保持良好的可读性。DaisyUI 的设计风格让这个提示看起来既美观又实用。'
        );
    };

    const handleMultipleToasts = () => {
        showToast.success('第一个提示');
        setTimeout(() => showToast.warning('第二个提示'), 500);
        setTimeout(() => showToast.info('第三个提示'), 1000);
        setTimeout(() => showToast.error('第四个提示'), 1500);
    };

    const handleTopCenterToast = () => {
        showToast.success('顶部居中的提示', {
            position: 'top-center'
        });
    };

    const handleBottomRightToast = () => {
        showToast.info('底部右侧的提示', {
            position: 'bottom-right'
        });
    };

    const handleNoAutoClose = () => {
        showToast.warning('这个提示不会自动关闭', {
            autoClose: false
        });
    };

    return (
        <div className="max-w-4xl mx-auto p-6">
            <header className="mb-8">
                <h1 className="text-3xl font-bold text-base-content mb-2">
                    DaisyUI Toast 演示
                </h1>
                <p className="text-base-content/70">
                    体验集成了 DaisyUI 设计风格的 react-toastify 通知系统
                </p>
            </header>

            <div className="grid gap-6">
                {/* 基础类型 */}
                <div className="card bg-base-100 shadow-lg">
                    <div className="card-body">
                        <h2 className="card-title text-lg mb-4">基础类型</h2>
                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
                            <button
                                className="btn btn-success"
                                onClick={handleSuccessToast}
                            >
                                成功提示
                            </button>

                            <button
                                className="btn btn-error"
                                onClick={handleErrorToast}
                            >
                                错误提示
                            </button>

                            <button
                                className="btn btn-warning"
                                onClick={handleWarningToast}
                            >
                                警告提示
                            </button>

                            <button
                                className="btn btn-info"
                                onClick={handleInfoToast}
                            >
                                信息提示
                            </button>

                            <button
                                className="btn btn-ghost"
                                onClick={handleDefaultToast}
                            >
                                默认提示
                            </button>
                        </div>
                    </div>
                </div>

                {/* 高级功能 */}
                <div className="card bg-base-100 shadow-lg">
                    <div className="card-body">
                        <h2 className="card-title text-lg mb-4">高级功能</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                            <button
                                className="btn btn-primary"
                                onClick={handleCustomToast}
                            >
                                自定义内容
                            </button>

                            <button
                                className="btn btn-accent"
                                onClick={handlePromiseToast}
                            >
                                Promise 提示
                            </button>

                            <button
                                className="btn btn-secondary"
                                onClick={handleLongMessage}
                            >
                                长文本提示
                            </button>
                        </div>
                    </div>
                </div>

                {/* 位置和行为 */}
                <div className="card bg-base-100 shadow-lg">
                    <div className="card-body">
                        <h2 className="card-title text-lg mb-4">位置和行为</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                            <button
                                className="btn btn-outline btn-success"
                                onClick={handleTopCenterToast}
                            >
                                顶部居中
                            </button>

                            <button
                                className="btn btn-outline btn-info"
                                onClick={handleBottomRightToast}
                            >
                                底部右侧
                            </button>

                            <button
                                className="btn btn-outline btn-warning"
                                onClick={handleNoAutoClose}
                            >
                                不自动关闭
                            </button>

                            <button
                                className="btn btn-outline btn-error"
                                onClick={handleMultipleToasts}
                            >
                                多个提示
                            </button>
                        </div>
                    </div>
                </div>

                {/* 特性说明 */}
                <div className="card bg-base-100 shadow-lg">
                    <div className="card-body">
                        <h2 className="card-title text-lg mb-4">特性说明</h2>
                        <div className="prose max-w-none">
                            <ul>
                                <li><strong>主题适配：</strong>自动适配 DaisyUI 的明亮/暗色主题</li>
                                <li><strong>响应式设计：</strong>在移动设备上自动调整宽度和位置</li>
                                <li><strong>流畅动画：</strong>使用 DaisyUI 的动画风格，支持悬停效果</li>
                                <li><strong>可拖拽：</strong>支持拖拽关闭功能</li>
                                <li><strong>无障碍：</strong>支持键盘导航和屏幕阅读器</li>
                                <li><strong>高度定制：</strong>支持自定义位置、时长、样式等</li>
                            </ul>
                        </div>
                    </div>
                </div>

                {/* 代码示例 */}
                <div className="card bg-base-100 shadow-lg">
                    <div className="card-body">
                        <h2 className="card-title text-lg mb-4">代码示例</h2>
                        <div className="mockup-code">
                            <pre data-prefix="1"><code>import &#123; showToast &#125; from '@/utils/toast';</code></pre>
                            <pre data-prefix="2"><code></code></pre>
                            <pre data-prefix="3"><code>// 基础用法</code></pre>
                            <pre data-prefix="4"><code>showToast.success('操作成功！');</code></pre>
                            <pre data-prefix="5"><code>showToast.error('操作失败！');</code></pre>
                            <pre data-prefix="6"><code></code></pre>
                            <pre data-prefix="7"><code>// 自定义选项</code></pre>
                            <pre data-prefix="8"><code>showToast.info('消息', &#123;</code></pre>
                            <pre data-prefix="9"><code>  position: 'top-center',</code></pre>
                            <pre data-prefix="10"><code>  autoClose: 3000</code></pre>
                            <pre data-prefix="11"><code>&#125;);</code></pre>
                        </div>
                    </div>
                </div>

                {/* DaisyUI 颜色变量测试 */}
                <div className="card bg-base-100 shadow-lg">
                    <div className="card-body">
                        <h2 className="card-title text-lg mb-4">DaisyUI 颜色变量检测</h2>
                        <ColorTest />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ToastDemo;

import { useLocation, useSearchParams } from "react-router-dom";
import { useAuthStore } from "../../models/auth";
import { useEffect } from "react";
import QueryString from "qs";

const WXOffiaccountLogin: React.FC = () => {
    const { actions } = useAuthStore();
    const location = useLocation();
    const [searchParams] = useSearchParams();

    useEffect(() => {
        const code = searchParams.get('code');
        const queryParams = QueryString.parse(location.search.slice(1));
        if (code) {
            actions.wxOffiaccountLogin(code, queryParams as any);
        } else {
            console.error('未获取到微信授权码');
        }
    }, [actions, searchParams]);

    return <div className="flex h-screen items-center justify-center">
        <div className="text-center">
            <h1 className="text-xl font-semibold mb-2">登录中...</h1>
            <p className="text-gray-500">正在处理微信登录，请稍候</p>
        </div>
    </div>;
};

export default WXOffiaccountLogin;
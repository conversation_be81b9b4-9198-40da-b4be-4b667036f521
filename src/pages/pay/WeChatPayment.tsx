import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import './wechatPayment.css';
import { isWeChatBrowser, isAlipayBrowser, isSupportedPaymentBrowser, initWeChatSDK, initiateWeChatPayment } from '../../utils/wechatPay';
import { getWechatJssdkParams, getAlipayForm } from '../../api/common';

interface PaymentParams {
  appId: string;
  timeStamp: string;
  nonceStr: string;
  package: string;
  signType: string;
  paySign: string;
  orderNo: string;
  amount: number;
  subject: string;
  body: string;
}

interface AlipayParams {
  amount: number;
  order_no: string;
  pay_url: string;
  subject: string;
}

type PaymentData = PaymentParams | AlipayParams;

// Type guards
const isWechatPaymentData = (data: PaymentData): data is PaymentParams => {
  return 'appId' in data;
};

const isAlipayPaymentData = (data: PaymentData): data is AlipayParams => {
  return 'pay_url' in data;
};

declare global {
  interface Window {
    WeixinJSBridge: any;
    wx: any;
    AlipayJSBridge: any;
  }
}

const Payment: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [paymentData, setPaymentData] = useState<PaymentData | null>(null);
  
  // Get trade_no_for_query and code from URL
  const tradeNoForQuery = searchParams.get('trade_no_for_query');
  const code = searchParams.get('code');
  
  // Fetch payment parameters
  const fetchPaymentParams = async (tradeNo: string, wxCode: string | null) => {
    setIsLoading(true);
    try {
      if (isWeChatBrowser()) {
        const response = await getWechatJssdkParams({
          params: {
            trade_no_for_query: tradeNo,
            code: wxCode
          }
        });
        
        if (response.data && response.data.code === 0 && response.data.data) {
          setPaymentData(response.data.data);
        } else {
          const errorMsg = response.data?.message || '获取支付参数失败';
          setError(errorMsg);
        }
      } else if (isAlipayBrowser()) {
        const response = await getAlipayForm({
          params: {
            trade_no_for_query: tradeNo
          }
        });
        
        if (response.data && response.data.code === 0 && response.data.data) {
          setPaymentData(response.data.data);
        } else {
          const errorMsg = response.data?.message || '获取支付参数失败';
          setError(errorMsg);
        }
      }
    } catch (err) {
      setError('获取支付参数失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Check if running in a supported payment browser
    if (!isSupportedPaymentBrowser()) {
      setError('请在微信或支付宝浏览器中打开此页面');
      return;
    }

    // Fetch payment parameters if trade_no_for_query is available
    if (tradeNoForQuery) {
      fetchPaymentParams(tradeNoForQuery, code);
    } else {
      setError('缺少订单号参数，无法获取支付参数');
    }
    
    // Load appropriate SDK based on browser
    if (isWeChatBrowser()) {
      const loadWeChatScript = () => {
        if (!window.wx) {
          const script = document.createElement('script');
          script.src = 'https://res.wx.qq.com/open/js/jweixin-1.6.0.js';
          script.async = true;
          document.body.appendChild(script);
        }
      };
      loadWeChatScript();
    }
  }, [tradeNoForQuery, code]);

  const handleConfirmPayment = async () => {
    if (!isSupportedPaymentBrowser()) {
      setError('请在微信或支付宝浏览器中打开此页面');
      return;
    }
    
    if (!paymentData || !tradeNoForQuery) {
      setError('支付信息不完整，无法进行支付');
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      if (isWeChatBrowser() && isWechatPaymentData(paymentData)) {
        // Initialize WeChat JSSDK with parameters from API
        await initWeChatSDK({
          debug: false,
          appId: paymentData.appId,
          timestamp: paymentData.timeStamp,
          nonceStr: paymentData.nonceStr,
          signature: paymentData.paySign,
          jsApiList: ['chooseWXPay']
        });
        
        // Initiate WeChat payment
        await initiateWeChatPayment({
          timestamp: paymentData.timeStamp,
          nonceStr: paymentData.nonceStr,
          package: paymentData.package,
          signType: paymentData.signType,
          paySign: paymentData.paySign
        });
        
        // Payment successful
        navigate(`/pay/success?amount=${paymentData.amount.toFixed(2)}&subject=${encodeURIComponent(paymentData.subject)}&order_no=${encodeURIComponent(paymentData.orderNo)}`);
      } else if (isAlipayBrowser() && isAlipayPaymentData(paymentData)) {
        // Redirect to Alipay payment page
        window.location.href = paymentData.pay_url;
        return; // Return early as we're redirecting
      }
    } catch (err) {
      setError('支付失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  // Show loading state
  if (isLoading && !paymentData) {
    return (
      <div className="wechat-payment-container">
        <div className="payment-content">
          <div className="loading-spinner">加载中...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="wechat-payment-container">
      <div className="payment-content">
        {paymentData ? (
          <>
            <div className="payment-amount">
              <span className="currency">¥</span>
              <span className="amount">{paymentData.amount.toFixed(2)}</span>
            </div>
            
            <div className="payment-details">
              <div className="payment-row">
                <div className="payment-label">商品名称</div>
                <div className="payment-value">{paymentData.subject}</div>
              </div>
              
              <div className="payment-row">
                <div className="payment-label">订单号</div>
                <div className="payment-value">
                  {isWechatPaymentData(paymentData) ? paymentData.orderNo : paymentData.order_no}
                </div>
              </div>
              
              {isWechatPaymentData(paymentData) && (
                <div className="payment-row">
                  <div className="payment-label">商品描述</div>
                  <div className="payment-value">{paymentData.body}</div>
                </div>
              )}
            </div>
          </>
        ) : (
          <div className="no-order-data">
            {error ? <div className="payment-error">{error}</div> : '未能获取支付信息'}
          </div>
        )}
        
        {error && <div className="payment-error">{error}</div>}
        
        <button 
          className="payment-button" 
          onClick={handleConfirmPayment}
          disabled={isLoading || !paymentData}
        >
          {isLoading ? '处理中...' : '立即支付'}
        </button>
      </div>
    </div>
  );
};

export default Payment; 
import React from 'react';
import { useSearchParams } from 'react-router-dom';
import './paymentSuccess.css';

const PaymentSuccess: React.FC = () => {
  const [searchParams] = useSearchParams();
  
  const amount = searchParams.get('amount') || '0.00';
  const subject = searchParams.get('subject') || '支付商品';
  const orderNo = searchParams.get('order_no') || '';

  return (
    <div className="payment-success-container">
      <div className="success-icon">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
          <path d="M0 0h24v24H0V0z" fill="none" />
          <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z" />
        </svg>
      </div>
      <h1 className="success-title">支付成功!</h1>
      <p className="success-message">您的订单已完成支付</p>
      <div className="success-details">
        <div className="detail-row">
          <span className="detail-label">商品名称</span>
          <span className="detail-value">{subject}</span>
        </div>
        {orderNo && (
          <div className="detail-row">
            <span className="detail-label">订单号</span>
            <span className="detail-value">{orderNo}</span>
          </div>
        )}
        <div className="detail-row">
          <span className="detail-label">支付金额</span>
          <span className="detail-value">¥{amount}</span>
        </div>
      </div>
    </div>
  );
};

export default PaymentSuccess; 
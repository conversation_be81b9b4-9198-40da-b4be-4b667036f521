.payment-success-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #fff;
  padding: 0 20px;
  text-align: center;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

.success-icon {
  width: 80px;
  height: 80px;
  background-color: #4caf50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.success-icon svg {
  width: 50px;
  height: 50px;
  color: white;
}

.success-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
}

.success-message {
  font-size: 16px;
  color: #666;
  margin-bottom: 30px;
}

.success-details {
  width: 100%;
  max-width: 300px;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 30px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid #e0e0e0;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  color: #666;
  font-size: 14px;
}

.detail-value {
  color: #333;
  font-weight: 500;
  font-size: 14px;
}

.back-button {
  background-color: #ff4d6a;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 12px 30px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  margin-bottom: 15px;
}

.auto-redirect {
  color: #999;
  font-size: 14px;
} 
/* WeChat Payment Styles */
.wechat-payment-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

/* Header Styles */
.payment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 15px;
  background-color: #f8f8f8;
  border-bottom: 1px solid #e0e0e0;
}

.back-button {
  font-size: 28px;
  font-weight: 300;
  cursor: pointer;
}

.payment-url {
  font-size: 16px;
  color: #333;
}

.more-options {
  font-size: 18px;
  letter-spacing: 2px;
  color: #333;
}

/* Payment Content Styles */
.payment-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 30px 15px;
  align-items: center;
  background-color: #fff;
}

.payment-amount {
  text-align: center;
  margin: 40px 0;
}

.currency {
  font-size: 32px;
  color: #000;
  font-weight: normal;
  margin-right: 5px;
}

.amount {
  font-size: 60px;
  font-weight: bold;
  color: #000;
}

.payment-details {
  width: 100%;
  margin-bottom: 40px;
}

.payment-row {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.payment-label {
  color: #333;
  font-size: 16px;
}

.payment-value {
  color: #666;
  font-size: 16px;
  text-align: right;
}

.payment-error {
  width: 100%;
  padding: 10px;
  background-color: #fff2f2;
  border: 1px solid #ffcdd2;
  color: #d32f2f;
  border-radius: 4px;
  margin-bottom: 15px;
  font-size: 14px;
  text-align: center;
}

.payment-button {
  width: 100%;
  padding: 15px 0;
  background-color: #ff4d6a;
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 18px;
  font-weight: 500;
  cursor: pointer;
  margin-top: 20px;
}

.payment-button:disabled {
  background-color: #ffb3c0;
  cursor: not-allowed;
}

/* Loading Styles */
/* .loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: #666;
  font-size: 16px;
  margin: 50px 0;
}

.loading-spinner::before {
  content: "";
  width: 24px;
  height: 24px;
  border: 3px solid #ddd;
  border-top-color: #ff4d6a;
  border-radius: 50%;
  margin-right: 10px;
  animation: spin 1s linear infinite;
} */

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.no-order-data {
  text-align: center;
  color: #666;
  margin: 50px 0;
  font-size: 16px;
  width: 100%;
}

/* Footer Styles */
.payment-footer {
  background-color: #000;
  color: #fff;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.footer-warning {
  display: flex;
  align-items: center;
  flex: 1;
}

.warning-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  background-color: #ff4d4d;
  color: white;
  border-radius: 50%;
  margin-right: 8px;
  font-size: 12px;
  font-weight: bold;
}

.use-full-service {
  color: #fff;
  white-space: nowrap;
} 
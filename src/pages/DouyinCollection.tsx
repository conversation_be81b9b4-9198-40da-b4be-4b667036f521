import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Modal } from 'react-daisyui';
import { toast } from 'react-toastify';
import { formatDateTime } from '@/utils';
import { useAuthStore } from '@/models/auth';
import api from '@/api';
import { apiClient } from '@/api/generated/client';
import type { components } from '@/api/generated/types';


// 使用后端返回的同步记录类型
type DouyinSyncRecord = components['schemas']['DouyinSyncRecord'];

// 分页信息类型
interface PaginationInfo {
  page: number;
  page_size: number;
  total: number;
  total_page: number;
}

const DouyinCollection: React.FC = () => {
  const { state, actions } = useAuthStore();
  const [cookieValue, setCookieValue] = useState('');
  const [isSyncing, setIsSyncing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const configModalRef = useRef<HTMLDialogElement>(null);
  const helpModalRef = useRef<HTMLDialogElement>(null);
  const syncConfirmModalRef = useRef<HTMLDialogElement>(null);
  const detailsModalRef = useRef<HTMLDialogElement>(null);

  // 同步记录相关状态
  const [syncRecords, setSyncRecords] = useState<DouyinSyncRecord[]>([]);
  const [recordsLoading, setRecordsLoading] = useState(false);
  const [recordsError, setRecordsError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    page_size: 20,
    total: 0,
    total_page: 0
  });

  // 同步记录详情相关状态
  const [syncRecordDetails, setSyncRecordDetails] = useState<any[]>([]);
  const [detailsLoading, setDetailsLoading] = useState(false);
  const [detailsError, setDetailsError] = useState<string | null>(null);
  const [selectedRecord, setSelectedRecord] = useState<DouyinSyncRecord | null>(null);
  const [detailsPagination, setDetailsPagination] = useState<PaginationInfo>({
    page: 1,
    page_size: 20,
    total: 0,
    total_page: 0
  });

  // 检查是否已配置 Cookie
  const isConfigured = Boolean(state.userInfo?.douyin_cookie);

  // 获取同步记录
  const fetchSyncRecords = async (page: number = 1, pageSize: number = 20) => {
    try {
      setRecordsLoading(true);
      setRecordsError(null);

      const { data, error } = await apiClient.GET('/client/user/douyin/getSyncRecords', {
        params: {
          query: {
            page,
            page_size: pageSize
          }
        }
      });

      if (error) {
        throw new Error(error.msg || '获取同步记录失败');
      }

      if (data?.code === 0) {
        setSyncRecords(data.data?.records || []);
        setPagination({
          page: data.data?.pagination?.page || 1,
          page_size: data.data?.pagination?.page_size || 20,
          total: data.data?.pagination?.total || 0,
          total_page: data.data?.pagination?.total_page || 0
        });
      } else {
        throw new Error(data?.msg || '获取同步记录失败');
      }
    } catch (error: any) {
      console.error('获取同步记录失败:', error);
      setRecordsError(error.message || '获取同步记录失败');
      setSyncRecords([]);
    } finally {
      setRecordsLoading(false);
    }
  };

  // 获取同步记录关联数据
  const fetchSyncRecordRelated = async (syncRecordUuid: string, page: number = 1, pageSize: number = 20) => {
    try {
      setDetailsLoading(true);
      setDetailsError(null);

      const { data, error } = await apiClient.GET('/client/user/douyin/getSyncRecordRelated', {
        params: {
          query: {
            sync_record_uuid: syncRecordUuid,
            page,
            page_size: pageSize
          }
        }
      });

      if (error) {
        throw new Error(error.msg || '获取同步记录详情失败');
      }

      if (data?.code === 0) {
        setSyncRecordDetails(data.data?.records || []);
        setDetailsPagination({
          page: data.data?.pagination?.page || 1,
          page_size: data.data?.pagination?.page_size || 20,
          total: data.data?.pagination?.total || 0,
          total_page: data.data?.pagination?.total_page || 0
        });
      } else {
        throw new Error(data?.msg || '获取同步记录详情失败');
      }
    } catch (error: any) {
      console.error('获取同步记录详情失败:', error);
      setDetailsError(error.message || '获取同步记录详情失败');
      setSyncRecordDetails([]);
    } finally {
      setDetailsLoading(false);
    }
  };

  // 格式化时间
  const formatTime = (timeString?: string) => {
    if (!timeString) return '未知时间';
    try {
      return formatDateTime(timeString);
    } catch {
      return timeString;
    }
  };



  // 获取状态显示信息
  const getStatusInfo = (status?: string) => {
    switch (status) {
      case 'SUCCESS':
        return { text: '成功', color: 'success' as const, icon: 'fa-check' };
      case 'FAILED':
        return { text: '失败', color: 'error' as const, icon: 'fa-times' };
      default:
        return { text: '未知', color: 'warning' as const, icon: 'fa-question' };
    }
  };

  // 组件挂载时获取用户信息和同步记录
  useEffect(() => {
    actions.getUserInfo();
    fetchSyncRecords();
  }, []);

  const handleConfigureCookie = () => {
    // 如果用户已有 cookie，则在 textarea 中显示当前值
    if (state.userInfo?.douyin_cookie) {
      setCookieValue(state.userInfo.douyin_cookie);
    } else {
      setCookieValue('');
    }
    configModalRef.current?.showModal();
  };

  const handleSaveCookie = async () => {
    if (!cookieValue.trim()) {
      toast.error('请输入有效的Cookie信息');
      return;
    }

    try {
      setIsLoading(true);
      // 调用真实的 API 保存 Cookie
      await actions.bindDouyinCookie(cookieValue);
      // 保存成功后重新获取用户信息以更新状态
      await actions.getUserInfo();
      configModalRef.current?.close();
      setCookieValue(''); // 清空输入框
      toast.success('Cookie 配置成功！');
    } catch (error: any) {
      console.error('保存Cookie失败:', error);
      toast.error(`保存失败: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBatchSync = () => {
    if (!isConfigured) {
      toast.error('请先配置抖音 Cookie');
      return;
    }

    // 显示同步确认模态框
    syncConfirmModalRef.current?.showModal();
  };

  const handleConfirmSync = async () => {
    setIsSyncing(true);

    try {
      // 调用真实的同步 API
      const response = await api.post('/client/user/douyin/syncCollects');

      if (response.data.code === 0) {
        toast.success('同步完成！已开始同步抖音收藏夹');
        // 同步成功后刷新同步记录
        setTimeout(() => {
          fetchSyncRecords();
        }, 1000);
      } else {
        toast.error(`同步失败: ${response.data.msg || '未知错误'}`);
      }
    } catch (error: any) {
      console.error('同步失败:', error);
      const errorData = error.response?.data;

      if (errorData?.code === 405003) {
        toast.error('抖音 Cookie 无效或已过期，请重新配置');
        handleConfigureCookie();
      } else if (errorData?.code === 405002) {
        toast.error('请先配置商业定位');
      } else {
        toast.error(`同步失败: ${errorData?.msg || error.message || '网络错误'}`);
      }
    } finally {
      setIsSyncing(false);
      syncConfirmModalRef.current?.close();
    }
  };

  const handleCancelCookie = () => {
    setCookieValue(''); // 清空输入框
    configModalRef.current?.close();
  };

  const openHelpModal = () => {
    helpModalRef.current?.showModal();
  };

  const handleViewDetails = async (record: DouyinSyncRecord) => {
    if (!record.uuid) {
      toast.error('同步记录ID不存在');
      return;
    }
    
    setSelectedRecord(record);
    // 重置详情状态
    setSyncRecordDetails([]);
    setDetailsError(null);
    setDetailsPagination({
      page: 1,
      page_size: 20,
      total: 0,
      total_page: 0
    });
    
    detailsModalRef.current?.showModal();
    
    // 获取详情数据
    await fetchSyncRecordRelated(record.uuid);
  };

  return (
    <div>
      <header className="mb-6">
        <h1 className="text-2xl font-bold">同步抖音收藏夹</h1>
        <p className="text-base-content opacity-60">一键同步你的抖音收藏夹，自动分析生成选题</p>
      </header>

      {/* 同步操作区 */}
      <div className="grid grid-cols-1 gap-6 mb-8">
        <Card className="bg-base-100 shadow-md">
          <Card.Body>
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-3">
                  <h3 className="text-xl font-bold">抖音收藏夹同步</h3>
                  <Badge color={isConfigured ? 'success' : 'error'}>
                    {isConfigured ? '已配置' : '未配置'}
                  </Badge>
                </div>
                <p className="text-base-content opacity-70 mb-3">
                  自动获取你的抖音收藏夹内容，添加至灵感收件箱
                </p>
                <div className="flex flex-col sm:flex-row sm:items-center gap-3 text-sm">
                  <div className="flex items-center gap-2">
                    <i className={`fas ${isConfigured ? 'fa-check-circle text-success' : 'fa-exclamation-circle text-error'}`}></i>
                    <span className="opacity-70">
                      {isConfigured ? 'Cookie已配置，可以开始同步' : '请先配置Cookie以启用同步功能'}
                    </span>
                  </div>
                  <div className="flex items-center gap-2 opacity-60">
                    <i className="fas fa-clock"></i>
                    <span>
                      最后更新：{state.userInfo?.update_time ?
                        formatDateTime(state.userInfo.update_time) :
                        '从未更新'
                      }
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex flex-col sm:flex-row gap-3">
                <Button 
                  className="bg-black text-white hover:bg-gray-800" 
                  onClick={handleConfigureCookie}
                >
                  <i className="fas fa-cog mr-2"></i>配置抖音 Cookie
                </Button>
                <Button 
                  color="primary"
                  onClick={handleBatchSync}
                  disabled={!isConfigured}
                  loading={isSyncing}
                >
                  <i className="fas fa-download mr-2"></i>批量同步
                </Button>
              </div>
            </div>
          </Card.Body>
        </Card>
      </div>

      {/* 同步历史 */}
      <Card className="bg-base-100 shadow-md">
        <Card.Body>
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-bold flex items-center gap-2">
              <i className="fas fa-history text-primary"></i>
              同步历史记录
            </h3>
          </div>

          {recordsError && (
            <div className="alert alert-error mb-4">
              <i className="fas fa-exclamation-triangle"></i>
              <div>
                <div className="font-medium">获取同步记录失败</div>
                <div className="text-sm opacity-80">{recordsError}</div>
              </div>
            </div>
          )}

          <div className="overflow-x-auto">
            <table className="table table-zebra">
              <thead>
                <tr className="bg-base-200">
                  <th className="font-semibold">同步时间</th>
                  <th className="font-semibold">新增素材</th>
                  <th className="font-semibold">同步状态</th>
                  <th className="font-semibold">操作</th>
                </tr>
              </thead>
              <tbody>
                {recordsLoading ? (
                  <tr>
                    <td colSpan={4} className="text-center py-8">
                      <div className="flex items-center justify-center gap-2">
                        <span className="loading loading-spinner loading-sm"></span>
                        <span>加载中...</span>
                      </div>
                    </td>
                  </tr>
                ) : syncRecords.length === 0 ? (
                  <tr>
                    <td colSpan={4} className="text-center py-8 text-base-content opacity-60">
                      <div className="flex flex-col items-center gap-2">
                        <i className="fas fa-inbox text-2xl"></i>
                        <span>暂无同步记录</span>
                      </div>
                    </td>
                  </tr>
                ) : (
                  syncRecords.map(record => {
                    const statusInfo = getStatusInfo(record.sync_status);
                    return (
                      <tr key={record.uuid || record.id} className="hover">
                        <td className="font-medium">{formatTime(record.sync_time)}</td>
                        <td>
                          <div className="flex items-center gap-2">
                            <i className={`fas ${statusInfo.icon} text-${statusInfo.color} text-xs`}></i>
                            <span>{record.sync_count || 0}个</span>
                          </div>
                        </td>
                        <td>
                          <Badge
                            color={statusInfo.color}
                            className="gap-1"
                          >
                            <i className={`fas ${statusInfo.icon} text-xs`}></i>
                            {statusInfo.text}
                          </Badge>
                        </td>
                        <td>
                          {record.sync_status === 'SUCCESS' ? (
                            <Button 
                              size="xs" 
                              color="primary" 
                              className="btn-outline"
                              onClick={() => handleViewDetails(record)}
                            >
                              查看详情
                            </Button>
                          ) : (
                            <Button
                              size="xs"
                              color="error"
                              className="btn-outline"
                              onClick={handleConfigureCookie}
                            >
                              重新配置
                            </Button>
                          )}
                        </td>
                      </tr>
                    );
                  })
                )}
              </tbody>
            </table>
          </div>

          {/* 分页信息 */}
          {pagination.total > 0 && (
            <div className="flex items-center justify-between mt-4 pt-4 border-t border-base-300">
              <div className="text-sm text-base-content opacity-60">
                共 {pagination.total} 条记录，第 {pagination.page} / {pagination.total_page} 页
              </div>
              <div className="flex items-center gap-2">
                <Button
                  size="xs"
                  className="btn-outline"
                  disabled={pagination.page <= 1 || recordsLoading}
                  onClick={() => fetchSyncRecords(pagination.page - 1, pagination.page_size)}
                >
                  上一页
                </Button>
                <Button
                  size="xs"
                  className="btn-outline"
                  disabled={pagination.page >= pagination.total_page || recordsLoading}
                  onClick={() => fetchSyncRecords(pagination.page + 1, pagination.page_size)}
                >
                  下一页
                </Button>
              </div>
            </div>
          )}
        </Card.Body>
      </Card>

      {/* Cookie配置模态框 */}
      <Modal ref={configModalRef} containerClasses="modal-bottom sm:modal-middle">
        <Modal.Header className="text-lg font-bold mb-4 flex items-center gap-2">
          <i className="fas fa-key text-primary"></i>
          配置抖音 Cookie
        </Modal.Header>
        <Modal.Body>
          <div className="form-control mb-4">
            <label className="label">
              <span className="label-text font-medium">抖音 Cookie 信息</span>
            </label>
            <textarea 
              className="textarea textarea-bordered h-32 focus:textarea-primary w-full" 
              placeholder="请粘贴抖音 Cookie"
              value={cookieValue}
              onChange={(e) => setCookieValue(e.target.value)}
            />
            <label className="label">
              <span className="label-text-alt">
                <button 
                  type="button"
                  className="link link-primary hover:link-hover text-xs"
                  onClick={openHelpModal}
                >
                  <i className="fas fa-question-circle mr-1"></i>不知道如何获取？查看教程
                </button>
              </span>
            </label>
          </div>
          
          <div className="bg-base-200 rounded-lg p-3 text-sm mb-6">
            <div className="flex items-start gap-2">
              <i className="fas fa-info-circle text-primary mt-0.5 flex-shrink-0"></i>
              <div className="space-y-1">
                <div>• 支持每天自动同步一次</div>
                <div>• 支持无限次手动同步</div>
              </div>
            </div>
          </div>
        </Modal.Body>
        <Modal.Actions>
          <form method="dialog" className="flex gap-3 w-full justify-end">
            <Button onClick={handleCancelCookie}>取消</Button>
            <Button
              className="bg-black text-white hover:bg-gray-800"
              onClick={handleSaveCookie}
              loading={isLoading}
              disabled={isLoading}
            >
              <i className="fas fa-save mr-2"></i>保存
            </Button>
          </form>
        </Modal.Actions>
      </Modal>

      {/* Cookie获取教程模态框 */}
      <Modal ref={helpModalRef} containerClasses="modal-bottom sm:modal-middle">
        <Modal.Header className="text-xl font-bold mb-6 flex items-center gap-2">
          <i className="fas fa-graduation-cap text-primary"></i>
          Cookie 获取教程
        </Modal.Header>
        <Modal.Body>
          <div className="space-y-6">
            <div className="alert alert-warning">
              <i className="fas fa-exclamation-triangle"></i>
              <div>
                <div className="font-medium">重要提醒</div>
                <div className="text-sm opacity-80">请确保在抖音官网（douyin.com）登录状态下获取 Cookie，并复制完整信息</div>
              </div>
            </div>
            
            <div className="steps steps-vertical lg:steps-horizontal w-full">
              <div className="step step-primary">登录抖音</div>
              <div className="step step-primary">打开开发者工具</div>
              <div className="step step-primary">找到网络请求</div>
              <div className="step step-primary">复制Cookie</div>
            </div>
            
            <div className="alert alert-success">
              <i className="fas fa-lightbulb"></i>
              <div>
                <div className="font-medium">小贴士</div>
                <div className="text-sm opacity-80">Cookie 通常很长，请确保复制完整。如果同步失败，可能是 Cookie 过期，请重新获取</div>
              </div>
            </div>
          </div>
        </Modal.Body>
        <Modal.Actions>
          <form method="dialog">
            <Button color="primary">
              <i className="fas fa-check mr-2"></i>我知道了
            </Button>
          </form>
        </Modal.Actions>
      </Modal>

      {/* 同步确认模态框 */}
      <Modal ref={syncConfirmModalRef} containerClasses="modal-bottom sm:modal-middle">
        <Modal.Body>
          <h3 className="text-lg font-bold mb-4">同步抖音收藏夹</h3>
          <div className="mb-4">
            <p className="mb-2">将同步你的抖音收藏夹中，包含"素材"字样的收藏夹</p>
            <div className="bg-base-200 rounded-lg p-3 text-sm">
              <div className="space-y-1">
                <div>• 自动获取收藏夹中的视频内容</div>
                <div>• 添加至灵感收件箱供后续使用</div>
                <div>• 同步过程可能需要几分钟时间</div>
              </div>
            </div>
          </div>
          <Modal.Actions className="flex gap-2">
            <Button
              disabled={isSyncing}
              onClick={() => syncConfirmModalRef.current?.close()}
            >
              取消
            </Button>
            <Button
              disabled={isSyncing}
              loading={isSyncing}
              className="bg-black text-white hover:bg-gray-800"
              onClick={handleConfirmSync}
            >
              开始同步
            </Button>
          </Modal.Actions>
        </Modal.Body>
      </Modal>

      {/* 同步记录详情模态框 */}
      <Modal ref={detailsModalRef} containerClasses="modal-bottom sm:modal-middle" className="!max-w-4xl">
        <h3 className="text-xl font-bold mb-4 flex items-center gap-2">
          <i className="fas fa-list-ul text-primary"></i>
          <span>
            {selectedRecord && formatTime(selectedRecord.sync_time)} 同步详情
          </span>
        </h3>
        
        <div className="divider text-sm opacity-70">新增素材列表</div>
        
        {detailsError && (
          <div className="alert alert-error mb-4">
            <i className="fas fa-exclamation-triangle"></i>
            <div>
              <div className="font-medium">获取详情失败</div>
              <div className="text-sm opacity-80">{detailsError}</div>
            </div>
          </div>
        )}
        
        <div className="overflow-x-auto max-h-96 overflow-y-auto">
          <table className="table table-sm table-zebra">
            <thead className="bg-base-200 sticky top-0">
              <tr>
                <th className="w-16">#</th>
                <th>标题</th>
                <th>作者</th>
                <th>发布时间</th>
              </tr>
            </thead>
            <tbody>
              {detailsLoading ? (
                <tr>
                  <td colSpan={5} className="text-center py-8">
                    <div className="flex items-center justify-center gap-2">
                      <span className="loading loading-spinner loading-sm"></span>
                      <span>加载中...</span>
                    </div>
                  </td>
                </tr>
              ) : syncRecordDetails.length === 0 ? (
                <tr>
                  <td colSpan={5} className="text-center py-8 text-base-content opacity-60">
                    <div className="flex flex-col items-center gap-2">
                      <i className="fas fa-inbox text-2xl"></i>
                      <span>暂无关联数据</span>
                    </div>
                  </td>
                </tr>
) : (
                syncRecordDetails.map((detail, index) => {
                  // 优先使用 douyin_aweme 数据，如果没有则使用原始数据
                  const awemeData = detail.douyin_aweme || {};
                  const title = awemeData.Title || detail.title || '未知标题';
                  const authorName = awemeData.Nickname || detail.author_name || '未知作者';
                  const avatar = awemeData.Avatar || detail.author_avatar;

                  // 安全处理发布时间
                  let publishTimeStr = '未知时间';
                  if (awemeData.CreateTime && typeof awemeData.CreateTime === 'number' && awemeData.CreateTime > 0) {
                    try {
                      const publishTime = awemeData.CreateTime * 1000;
                      publishTimeStr = formatTime(new Date(publishTime).toISOString());
                    } catch (error) {
                      console.warn('日期转换失败:', awemeData.CreateTime, error);
                      publishTimeStr = '时间格式错误';
                    }
                  } else if (detail.publish_time) {
                    publishTimeStr = formatTime(detail.publish_time);
                  }

                  return (
                    <tr key={detail.id || index} className="hover">
                      <td>{(detailsPagination.page - 1) * detailsPagination.page_size + index + 1}</td>
                      <td className="font-medium">
                        <div className="max-w-xs truncate" title={title}>
                          {title}
                        </div>
                      </td>
                      <td>
                        <div className="flex items-center gap-2">
                          {avatar && (
                            <img
                              src={avatar}
                              alt=""
                              className="w-6 h-6 rounded-full"
                            />
                          )}
                          <span className="truncate">{authorName}</span>
                        </div>
                      </td>
                      <td className="text-sm">
                        {publishTimeStr}
                      </td>
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>
        
        {/* 分页信息 */}
        {detailsPagination.total > 0 && (
          <div className="flex items-center justify-between mt-4 pt-4 border-t border-base-300">
            <div className="text-sm text-base-content opacity-60">
              共 {detailsPagination.total} 条素材，第 {detailsPagination.page} / {detailsPagination.total_page} 页
            </div>
            <div className="flex items-center gap-2">
              <Button
                size="xs"
                className="btn-outline"
                disabled={detailsPagination.page <= 1 || detailsLoading}
                onClick={() => selectedRecord && fetchSyncRecordRelated(selectedRecord.uuid!, detailsPagination.page - 1, detailsPagination.page_size)}
              >
                上一页
              </Button>
              <Button
                size="xs"
                className="btn-outline"
                disabled={detailsPagination.page >= detailsPagination.total_page || detailsLoading}
                onClick={() => selectedRecord && fetchSyncRecordRelated(selectedRecord.uuid!, detailsPagination.page + 1, detailsPagination.page_size)}
              >
                下一页
              </Button>
            </div>
          </div>
        )}
        
        <div className="modal-action">
          <form method="dialog">
            <Button className="btn">关闭</Button>
          </form>
        </div>
      </Modal>
    </div>
  );
};

export default DouyinCollection;

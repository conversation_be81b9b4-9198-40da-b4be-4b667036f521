import { RouterProvider } from 'react-router-dom';
import { router } from './routes';
import { Theme } from 'react-daisyui';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { Helmet } from "react-helmet";
import { useDocumentStore } from './models/document';

function App() {
  const { state } = useDocumentStore();

  return (
    <Theme dataTheme="light">
      <Helmet>
        <title>{state.document.title}</title>
      </Helmet>
      <div className="bg-base-200">
        <RouterProvider router={router} />
      </div>
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="colored"
        toastClassName="toast-daisyui"
        progressClassName="toast-progress-daisyui"
      />
    </Theme>
  );
}

export default App; 
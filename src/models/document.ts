import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'

interface DocumentState {
    state: {
        document: {
            title: string;
        }
    };
    reducers: {
        setDocumentTitle: (title: string) => void;
    };
    actions: {};
}

const DEFAULT_DOCUMENT_NAME = '起号助手'

// 使用类型断言解决immer类型问题
export const useDocumentStore = create<DocumentState>()(
    immer((set) => ({
        state: {
            document: {
                title: DEFAULT_DOCUMENT_NAME
            }
        },
        reducers: {
            setDocumentTitle: (title: string = DEFAULT_DOCUMENT_NAME) => {
                set((state) => {
                    state.state.document.title = title
                })
            }
        },
        actions: {}
    }))
)
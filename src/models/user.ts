import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import api from '@/api';


interface UserStore {
    state: {
        dataStats: any,
    };
    actions: {
        getDataStats: () => Promise<void>;
    };
}

export const useUserStore = create<UserStore>()(
    immer((set) => ({
        state: {
            dataStats: {}
        },
        reducers: {},
        actions: {
            async getDataStats() {
                const response = await api.get('/client/user/stats/getStats');
                set(store => {
                    store.state.dataStats = response.data.data
                });
                return response.data.data;
            },
        }
    }))
)
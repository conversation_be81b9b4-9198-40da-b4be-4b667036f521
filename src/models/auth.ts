import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { persist } from 'zustand/middleware'
import api from '../api';
import { router } from '../routes';
import { toast } from 'react-toastify';
import { ROUTES } from '@/routes/constants';

interface UserInfo {
    commercial_pos: string;
    create_time: string;
    douyin_collection_cursor: string;
    douyin_cookie: string;
    douyin_id: string;
    douyin_profile: string;
    identity: string;
    is_deleted: number;
    role: string;
    role_expiration_date: string;
    telephone: string;
    update_time: string;
    uuid: string;
    wx_avatar: string;
    wx_gender: string;
    wx_nickname: string;
    wx_open_id: string;
    wx_union_id: string;
}

interface AuthStore {
    state: {
        token: string;
        userInfo?: UserInfo;
        isLoggedIn: boolean;
        isLoading: boolean;
        sourcePath: string;
    };
    reducers: {
        setSourcePath: (sourcePath: string) => void;
    };
    actions: {
        bindDouyinId(douyin_id: string): Promise<void>;
        bindDouyinCookie(douyin_cookie: string): Promise<void>;
        wrapperUpdate(key: string, value: string): Promise<void>;
        updateUserInfo(key: string, value: string): Promise<void>;
        getUserInfo(): Promise<void>;
        wxLogin: (code: string, queryParams: { code: string, source_path?: string }) => Promise<void>;
        wxOffiaccountLogin: (code: string, queryParams: { code: string, source_path?: string }) => Promise<void>;
        logout: () => Promise<void>;
        handleCommonAfterLogin: (sourcePath?: string) => void;
    };
}

export const useAuthStore = create<AuthStore>()(
    immer(
        persist(
            (set, get) => ({
                state: {
                    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************._kqRPvP-kQouMFgYQTxiq7XyQs-IqIh9galZHJ0CF9I",
                    "userInfo": {
                        "commercial_pos": "测试12",
                        "create_time": "2025-04-23 11:56:40",
                        "douyin_collection_cursor": "{}",
                        "douyin_cookie": "bd_ticket_guard_client_web_domain=2; passport_csrf_token=35dedeb9205ac05ee35b100be7658657; passport_csrf_token_default=35dedeb9205ac05ee35b100be7658657; UIFID_TEMP=37c879f32637ebc5496b02d59029bbf7616da5849e10d44e1e2ee2dac28cdd6bc04a53eb7b92dd7fcea951cff9fd85b18df3d04a40bc90a3d1fd7afda4e619a416a501a12b65d4114fc2c64ce2a517b9; x-web-secsdk-uid=2659b468-38e5-4bf1-8066-7df99b8d34c5; dy_swidth=3008; dy_sheight=1692; hevc_supported=true; UIFID=37c879f32637ebc5496b02d59029bbf7616da5849e10d44e1e2ee2dac28cdd6bc04a53eb7b92dd7fcea951cff9fd85b17fac0a16e6dd1ab9bdee030cde0a74c6da8a582d0396b811ef94a9d0bf14f1de6ee233ad821dbe92bf3b6f5376a95e6723dc5e1927ffe0ae7b21113cfe822a45e2469ec7e7931dffcc4a2b21380f619e5b46f4e00f4dfd4693d1ade93fe0196b35ce28a865a78de8548e9c550d5eaa3e; is_dash_user=1; douyin.com; device_web_cpu_core=2; device_web_memory_size=8; passport_mfa_token=CjeH%2F8hbZwxlJbvnb0XvfoXbcwX6xMId005qucNLeKJdfrHbXOrRgCAeXYrcOe%2BX33pKqQj2yIpcGkoKPDeZjVVfzrOl%2BkAAtFgp7LaG71%2FE5Dq%2BuCkcU9NLjVshjZB8VztCTJLfRtviY45QytBlqXFJDJAqaUpF%2BBDzue4NGPax0WwgAiIBA351hLg%3D; d_ticket=2fa9a71b13c63490b593cf49f793743ee3876; n_mh=XN-wT6okQcwIAv5AmXX_N01iHgwgFD2o_XPxJxRQouM; passport_auth_status=dbc2384ec6c7f23d2bd8cc5b2628c230%2C; passport_auth_status_ss=dbc2384ec6c7f23d2bd8cc5b2628c230%2C; is_staff_user=false; SelfTabRedDotControl=%5B%5D; SEARCH_RESULT_LIST_TYPE=%22single%22; csrf_session_id=708c5e8ba7506c1414040960c6a52413; passport_assist_user=CkHg6xc1dSqmbkSU-OM1zzv9eELYiZM8MP_ZtF8KYB53R-RJe1Sfvn5nOmdvWPTt2tPbn89j6Q9K0bYrX3L3j5ygpBpKCjy9IfLEZn-fVWy48lvB4L0oskNz_Q1-xXS_i8sKOkWmrqy-8dCFsmEJQPHtg9hjc5tIglP-z54fBE-Zc4UQ8LnuDRiJr9ZUIAEiAQMLNzpF; sid_guard=2035b1817b34658400e9acc8f89fae68%7C1744361276%7C5184000%7CTue%2C+10-Jun-2025+08%3A47%3A56+GMT; uid_tt=df6a2cf2e0630ccb3f8743b2f3310f91; uid_tt_ss=df6a2cf2e0630ccb3f8743b2f3310f91; sid_tt=2035b1817b34658400e9acc8f89fae68; sessionid=2035b1817b34658400e9acc8f89fae68; sessionid_ss=2035b1817b34658400e9acc8f89fae68; sid_ucp_v1=1.0.0-KGFlMGI1MzJkNzIyMmY3ZTEwYjE2N2JiNGFhNmRjNjcxMzhjNGI0MzcKIQjYtOCTq_SNAxC8ruO_BhjvMSAMMJ3RrPEFOAVA-wdIBBoCaGwiIDIwMzViMTgxN2IzNDY1ODQwMGU5YWNjOGY4OWZhZTY4; ssid_ucp_v1=1.0.0-KGFlMGI1MzJkNzIyMmY3ZTEwYjE2N2JiNGFhNmRjNjcxMzhjNGI0MzcKIQjYtOCTq_SNAxC8ruO_BhjvMSAMMJ3RrPEFOAVA-wdIBBoCaGwiIDIwMzViMTgxN2IzNDY1ODQwMGU5YWNjOGY4OWZhZTY4; login_time=1744361276939; __security_mc_1_s_sdk_sign_data_key_web_protect=3f0ad123-4c7b-87af; _bd_ticket_crypt_cookie=95ac936027c8af0cd3645df46642708f; passport_fe_beating_status=true; ttwid=1%7CUbK7QUsr8dFe0wLEPjpmykfawSz_v0TJ3zFcwj9NEq8%7C1745475512%7Cfa006656b7aa5d48263a29df7338a93d7ef334a96e5fba9af71db7025f8bca1d; fpk1=U2FsdGVkX1+vwNDFM404m+qWP5br04A8yNN2U9+SvUtdgMRRrwfxuzrLtZA7Ojx9XhMy+EpC2i1uzgvjj7Sy5g==; fpk2=d6dcc0a6def5582f8d3a9f7f2addb88b; __security_mc_1_s_sdk_crypt_sdk=49299ef0-4c61-98bc; s_v_web_id=verify_m9zc1afl_ZF8rAdUc_6luc_4f0h_BUuF_5QKl8bonriuJ; __ac_nonce=0680f1e4f005b3437dfc1; __ac_signature=_02B4Z6wo00f01VoZL8gAAIDAcx2xtyR7ZfVaOStAAD6N94; __security_mc_1_s_sdk_cert_key=bc62dab9-4881-abcc; strategyABtestKey=%************.018%22; volume_info=%7B%22isUserMute%22%3Afalse%2C%22isMute%22%3Atrue%2C%22volume%22%3A0%7D; biz_trace_id=6cbbfc02; publish_badge_show_info=%220%2C0%2C0%2C1745821266543%22; xg_device_score=7.621137179832504; download_guide=%221%2F20250428%2F1%22; stream_player_status_params=%22%7B%5C%22is_auto_play%5C%22%3A0%2C%5C%22is_full_screen%5C%22%3A0%2C%5C%22is_full_webscreen%5C%22%3A0%2C%5C%22is_mute%5C%22%3A1%2C%5C%22is_speed%5C%22%3A1%2C%5C%22is_visible%5C%22%3A0%7D%22; stream_recommend_feed_params=%22%7B%5C%22cookie_enabled%5C%22%3Atrue%2C%5C%22screen_width%5C%22%3A3008%2C%5C%22screen_height%5C%22%3A1692%2C%5C%22browser_online%5C%22%3Atrue%2C%5C%22cpu_core_num%5C%22%3A10%2C%5C%22device_memory%5C%22%3A8%2C%5C%22downlink%5C%22%3A7.25%2C%5C%22effective_type%5C%22%3A%5C%224g%5C%22%2C%5C%22round_trip_time%5C%22%3A100%7D%22; FOLLOW_LIVE_POINT_INFO=%22MS4wLjABAAAADY7u-UqfGogzrFtNghKD7Rgnz1QnWBJUrBA4AYrCX20zcMMCg1d8xoslUJiVTQyP%2F1745856000000%2F0%2F0%2F1745822037021%22; FOLLOW_NUMBER_YELLOW_POINT_INFO=%22MS4wLjABAAAADY7u-UqfGogzrFtNghKD7Rgnz1QnWBJUrBA4AYrCX20zcMMCg1d8xoslUJiVTQyP%2F1745856000000%2F0%2F1745821437021%2F0%22; bd_ticket_guard_client_data=eyJiZC10aWNrZXQtZ3VhcmQtdmVyc2lvbiI6MiwiYmQtdGlja2V0LWd1YXJkLWl0ZXJhdGlvbi12ZXJzaW9uIjoxLCJiZC10aWNrZXQtZ3VhcmQtcmVlLXB1YmxpYy1rZXkiOiJCRTBpUFZaOXhjZTZkWnpZa2dwZ214bXV1R01Od2JMSFJCRnZLdi9uOWpzRm1Zc2xSU3dUK28rcUtLK0pzOExUcHhNUFpUc3NjQVlNOS9yRUsvWU9NWXM9IiwiYmQtdGlja2V0LWd1YXJkLXdlYi12ZXJzaW9uIjoyfQ%3D%3D; home_can_add_dy_2_desktop=%221%22; IsDouyinActive=true; odin_tt=c1f92e914aea77704b86e9bf7423a9e5edcd434638bbf6bfea2430bf30364b6898f6f7b6c30e0f20553022010edf7ae416a16de1c3e5d97ba0c48e0a145e3748",
                        "douyin_id": "chon404",
                        "douyin_profile": "{\"user_id\": \"bihhfjaahhigjgcj\", \"aweme_id\": \"chon404\", \"aweme_url\": \"https://www.douyin.com/user/MS4wLjABAAAAQ_gmWk9ipiBo-eeyHOlsjsRSw8cUSuGBcRMrgGBEwt9lOj1XHlLMK7sL2uCyL3u_\", \"user_name\": \"崔正不焦虑\", \"item_count\": \"0\", \"like_count\": \"0\", \"follow_count\": \"1008\", \"first_tag_name\": \"未分类（投稿数不足）\", \"user_head_logo\": \"https://p11.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-avt-0015_7b90cbe396c9a5fffa26f24251498fb1.jpeg?from=**********\", \"second_tag_name\": \"\"}",
                        "identity": "CREATOR",
                        "is_deleted": 0,
                        "role": "PAID",
                        "role_expiration_date": "2026-04-25 00:00:00",
                        "telephone": "",
                        "update_time": "2025-05-31 21:38:36",
                        "uuid": "18e0bd37d19142fe9623707a1e6b28fe",
                        "wx_avatar": "https://thirdwx.qlogo.cn/mmopen/vi_32/DYAIOgq83eouXZBJkWG5YQ1R4yBic5U6erPv6Fuiasz4ob55lJKDdhW3v5V3dQ0micFliaPMes55xy1ibFmDxfldIIw/132",
                        "wx_gender": "UNKNOWN",
                        "wx_nickname": "kokoro",
                        "wx_open_id": "oAXAk6bCC6rz0bRm6RQMFc70Vg8c",
                        "wx_union_id": "oytfP60HUl2MPlOmpLRU2B3tgvxw"
                    },
                    "isLoggedIn": true,
                    "isLoading": false,
                    "sourcePath": "/"
                  },
                reducers: {
                    setSourcePath: (sourcePath: string) => {
                        set(store => {
                            store.state.sourcePath = sourcePath;
                        });
                    }
                },
                actions: {
                    handleCommonAfterLogin(sourcePath?: string) {
                        const handlePath = sourcePath?.includes(ROUTES.LOGIN) ? ROUTES.HOME : sourcePath
                        const targetPath = handlePath || ROUTES.HOME
                        router.navigate(targetPath.replace(new RegExp(`^${router.basename}`), '/'), { replace: true });
                    },
                    async wxLogin(code, queryParams) {
                        const response = await api.post('/client/user/wxLogin', { code });
                        const token = response.data.data.token;

                        set(store => {
                            store.state.token = token;
                            store.state.userInfo = response.data.data.user_info;
                            store.state.isLoggedIn = true;
                        });
                        get().actions.handleCommonAfterLogin(queryParams.source_path)
                    },

                    async wxOffiaccountLogin(code, queryParams) {
                        const response = await api.post('/client/user/wxOAuthLogin', { code });
                        const token = response.data.data.token;


                        set(store => {
                            store.state.token = token;
                            store.state.userInfo = response.data.data.user_info;
                            store.state.isLoggedIn = true;
                        });
                        get().actions.handleCommonAfterLogin(queryParams.source_path)
                    },

                    async getUserInfo() {
                        const response = await api.get('/client/user/getUserinfo');
                        set(store => {
                            const identity = response.data.data.identity ? response.data.data.identity : 'CREATOR';
                            store.state.userInfo = { ...response.data.data, identity };
                        });
                        return response.data.data;
                    },

                    async wrapperUpdate(key: string, value: string) {
                        set(store => {
                            store.state.isLoading = true;
                        });
                        try {
                            if (key == 'douyin_id') {
                                await get().actions.bindDouyinId(value);
                            } else if (key == 'douyin_cookie') {
                                await get().actions.bindDouyinCookie(value);
                            } else {
                                await get().actions.updateUserInfo(key, value);
                            }
                        } finally {
                            set(store => {
                                store.state.isLoading = false;
                            });
                        }
                    },

                    async bindDouyinId(douyin_id: string) {
                        const response = await api.post('/client/user/trendinsight/bindDouyinId', { keyword: douyin_id });
                        if (response.data.code == 0) {
                            toast('绑定成功');
                            get().actions.getUserInfo()
                        } else {
                            toast(response.data.message);
                        }
                        return response.data.data;
                    },

                    async bindDouyinCookie(douyinCookie: string) {
                        const response = await api.post('/client/user/douyin/bindDouyinCookie', {
                            cookies: douyinCookie
                        });
                        if (response.data.code == 0) {
                            toast('绑定成功');
                            get().actions.getUserInfo()
                        } else {
                            toast(response.data.message);
                        }
                        return response.data.data;
                    },

                    async updateUserInfo(key: string, value: string) {
                        const response = await api.post('/client/user/updateUserinfo', { [key]: value });
                        if (response.data.code == 0) {
                            toast('更新成功');
                            get().actions.getUserInfo()
                        } else {
                            toast(response.data.message);
                        }
                        return response.data.data;
                    },

                    async logout() {
                        try {
                            // Call the logout API endpoint first
                            await api.post('/client/user/logout');
                            // Continue with logout regardless of API response
                        } finally {
                            set(store => {
                                store.state.token = '';
                                store.state.userInfo = {} as any;
                                store.state.isLoggedIn = false;
                            });

                            const queryParamsText = new URLSearchParams({
                                source_path: router.state.location.pathname,
                            }).toString()
                            router.navigate(`${ROUTES.LOGIN}?${queryParamsText}`, { replace: true })
                        }
                    }
                }
            }),
            {
                name: 'qihaozhushou_auth', // name of the item in the storage (must be unique)
                partialize: (state) => ({
                    state: {
                        ...state.state,
                        isLoading: false
                    },
                }),
            },
        )
    )
)
import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { EventSource } from 'eventsource'
import { useAuthStore } from './auth'
import { API_URL } from '@/config';
import api from '@/api';
import { toast } from 'react-toastify';

enum EventType {
    START = 'START',
    END = 'END',
    ERROR = 'ERROR',
    DOING = 'DOING',
}

interface AssetsState {
    state: {
        isConnecting: boolean;
        unfinishedStats: Record<string, any>;
    };
    actions: {
        getUnfinishedStats: () => Promise<void>;
        startAssetsStatusStream: () => void;
        addAsset: (values: any) => Promise<any>;
    };
}

const ChangeTypeMap = {
    ANALYSIS: '解析',
    GENERATION: '选题生成'
}

const ChangeStatusMap = {
    DOING: '开始',
    SUCCESS: '完成',
}


export const useAssetsStore = create<AssetsState>()(
    immer((set, get) => ({
        state: {
            isConnecting: false,
            unfinishedStats: {
                analysis: 0,
                generation: 0
            },
        },
        reducers: {},
        actions: {
            async getUnfinishedStats() {
                const response = await api.get('/client/assets/getUnfinishedStats');
                set((store) => {
                    store.state.unfinishedStats = response.data.data;
                })
                // if (response.data.data.generation > 0) {
                //     get().actions.startAssetsStatusStream();
                // }
                return response.data.data;
            },
            async addAsset(values: any) {
                const response = await api.post('/client/assets/add', values);
                // get().actions.startAssetsStatusStream();
                return response.data;
            },
            startAssetsStatusStream() {
                if (get().state.isConnecting) return;


                set(store => {
                    store.state.isConnecting = true
                })
                const evtSource = new EventSource(API_URL + '/client/assets/getStartAssetsStatusStream', {
                    fetch: (input, init) => {
                        const { state } = useAuthStore.getState()
                        return fetch(input, {
                            ...init,
                            headers: {
                                Authorization: state.token,
                            },
                        })
                    }
                });
                evtSource.addEventListener('message', (event) => {
                    const data = JSON.parse(event.data)
                    console.log('data: ', data)
                    if (data.event_type == EventType.START) { 
                        // 开始事件处理逻辑
                    }
                    else if ([EventType.ERROR, EventType.END].includes(data.event_type)) {
                        set(store => {
                            store.state.isConnecting = false
                        })
                        evtSource.close()
                    }
                    if (![EventType.ERROR, EventType.START].includes(data.event_type)) {
                        toast('  有一条素材' + (ChangeStatusMap as any)[data.change_status] + (ChangeTypeMap as any)[data.change_type])
                        set(store => {
                            store.state.unfinishedStats.generation = data.analysis_count + data.generation_count
                        })
                    }
                })
            }
        }
    }))
)
import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import api from '../api'

interface AuthInfo {
    expires_in: number;
    access_token: string;
}

interface CozeState {
    state: {
        authInfo: AuthInfo;
    };
    reducers: Record<string, unknown>;
    actions: {
        getSessionToken: () => Promise<string>;
        onceGetSessionToken: () => Promise<string>;
    };
}

// 使用类型断言解决immer类型问题
export const useCozeStore = create<CozeState>()(
    immer((set, get) => ({
        state: {
            authInfo: {
                expires_in: 0,
                access_token: ''
            }
        },
        reducers: {},
        actions: {
            async getSessionToken() {
                const response = await api.get('/client/tools/oauth/getSessionToken')
                set((store) => {
                    store.state.authInfo = response.data.data
                })
                return response.data.data.access_token
            },
            async onceGetSessionToken() {
                const model = get();
                if (!model.state.authInfo.access_token) {
                    return await model.actions.getSessionToken()
                }
                return model.state.authInfo.access_token
            }
        }
    }))
)
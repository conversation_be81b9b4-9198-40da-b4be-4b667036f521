import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { apiClient } from '@/api/generated/client'
import type { components } from '@/api/generated/types'

// API 响应类型定义
type VideoRelatedStatsResponse = components['schemas']['VideoRelatedStatsResponse']

interface InboxState {
  state: {
    // 统计数据
    videoRelatedStats: VideoRelatedStatsResponse['data'] | null
    syncStats: any | null

    // 加载状态
    isLoadingVideoStats: boolean
    isLoadingStats: boolean

    // 错误状态
    videoStatsError: string | null
    statsError: string | null

    // 数据更新时间戳，用于缓存策略
    lastVideoStatsUpdate: number | null
  }
  actions: {
    // 统计数据相关
    fetchVideoRelatedStats: (force?: boolean) => Promise<void>

    // 清理缓存
    clearStatsCache: () => void

    // 重置错误状态
    clearErrors: () => void
  }
}

// 缓存有效期：5分钟
const CACHE_DURATION = 5 * 60 * 1000

export const useInboxStore = create<InboxState>()(
  immer((set, get) => ({
    state: {
  // 统计数据
  videoRelatedStats: null,
  syncStats: null,

  // 加载状态
  isLoadingVideoStats: false,
  isLoadingStats: false,

  // 错误状态
  videoStatsError: null,
  statsError: null,

  // 数据更新时间戳
  lastVideoStatsUpdate: null
},
    actions: {
      // 获取视频相关统计数据
      async fetchVideoRelatedStats(force = false) {
        const now = Date.now()
        const { state } = get()

        // 检查缓存是否有效
        if (!force && state.lastVideoStatsUpdate && (now - state.lastVideoStatsUpdate) < CACHE_DURATION && state.videoRelatedStats) {
          return
        }

        try {
          set((store) => {
            store.state.isLoadingVideoStats = true
            store.state.videoStatsError = null
          })

          const { data, error } = await apiClient.GET('/client/user/douyin/getVideoRelatedStats')

          if (error) {
            throw new Error(error.msg || '获取视频统计失败')
          }

          if (data?.code === 0) {
            set((store) => {
              store.state.videoRelatedStats = data.data || null
              store.state.videoStatsError = null
              store.state.lastVideoStatsUpdate = now
            })
          } else {
            throw new Error(data?.message || '获取视频统计失败')
          }
        } catch (error: unknown) {
          const errorMessage = error instanceof Error ? error.message : '获取视频统计失败'
          console.error('获取视频统计失败:', error)
          set((store) => {
            store.state.videoStatsError = errorMessage
            store.state.videoRelatedStats = null
          })
        } finally {
          set((store) => {
            store.state.isLoadingVideoStats = false
          })
        }
      },

      // 清理缓存
      clearStatsCache() {
        set((store) => {
          store.state.lastVideoStatsUpdate = null
        })
      },

      // 重置错误状态
      clearErrors() {
        set((store) => {
          store.state.videoStatsError = null
        })
      }
    }
  }))
)

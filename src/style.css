@import "tailwindcss";
/* 吃了亏 */
@source "../node_modules/react-daisyui";
@plugin "@tailwindcss/typography";
@plugin "daisyui";


.prose table {
    display: block;
    max-width: -moz-fit-content;
    max-width: fit-content;
    margin: 0 auto;
    overflow-x: auto;
    white-space: nowrap;
}

:root {
    interpolate-size: allow-keywords;
    /* 👈 允许 width: auto transication 动画 */
}

.drawer, .drawer-side {
    z-index: 999;
}

.menu-item {
    font-size: 0.95rem;
    padding: 0.75rem 1rem;
}

.menu-item i {
    font-size: 1rem;
    margin-right: 0.75rem;
    width: 1.5rem;
    text-align: center;
}

.menu-active {
    background-color: #e5e7eb !important;
    color: #000000 !important;
    font-weight: 500;
    box-shadow: none !important;
}

.user-profile {
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

.user-profile:hover {
    background-color: #f3f4f6;
}

/* Inbox 页面样式 */
.inspiration-card {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    cursor: pointer;
    min-width: 350px;
}

.inspiration-card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border-color: #d1d5db;
    transform: translateY(-1px);
}

/* 子菜单样式 */
.submenu-item {
    transition: all 0.2s ease;
    border-radius: 0.5rem;
    position: relative;
}

.submenu-item:hover {
    transform: translateX(2px);
}

.submenu-item.active {
    background-color: #e5e7eb;
    font-weight: 500;
}

:root {
    /* DaisyUI Toast 主题颜色配置 */
    --toastify-color-light: var(--b1);
    --toastify-color-dark: var(--b3);
    --toastify-color-info: var(--color-info);
    --toastify-color-success: var(--color-success);
    --toastify-color-warning: var(--color-warning);
    --toastify-color-error: var(--color-error);
    --toastify-color-transparent: rgba(255, 255, 255, 0.7);

    --toastify-icon-color-info: var(--color-base-content);
    --toastify-icon-color-success: var(--color-base-content);
    --toastify-icon-color-warning: var(--color-base-content);
    --toastify-icon-color-error: var(--color-base-content);
    
    /* DaisyUI 尺寸和间距 - 调整为更小的尺寸 */
    --toastify-container-width: fit-content;
    --toastify-toast-width: 280px;
    --toastify-toast-offset: 0.75rem;
    --toastify-toast-top: max(var(--toastify-toast-offset), env(safe-area-inset-top));
    --toastify-toast-right: max(var(--toastify-toast-offset), env(safe-area-inset-right));
    --toastify-toast-left: max(var(--toastify-toast-offset), env(safe-area-inset-left));
    --toastify-toast-bottom: max(var(--toastify-toast-offset), env(safe-area-inset-bottom));
    --toastify-toast-background: var(--color-base-100);
    --toastify-toast-padding: 0.75rem;
    --toastify-toast-min-height: 3rem;
    --toastify-toast-max-height: 600px;
    --toastify-toast-bd-radius: var(--radius-box, 0.5rem);
    --toastify-toast-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --toastify-font-family: inherit;
    --toastify-font-size: 0.75rem;
    --toastify-line-height: 1.3;
    --toastify-font-weight: 500;
    --toastify-z-index: 9999;
    --toastify-text-color-light: var(--color-base-content);
    --toastify-text-color-dark: var(--color-base-content);

    /* DaisyUI 主题色文字颜色 - 使用基础内容颜色 */
    --toastify-text-color-info: var(--color-base-content);
    --toastify-text-color-success: var(--color-base-content);
    --toastify-text-color-warning: var(--color-base-content);
    --toastify-text-color-error: var(--color-base-content);

    --toastify-spinner-color: var(--color-base-content);
    --toastify-spinner-color-empty-area: var(--color-base-content);
    --toastify-color-progress-light: linear-gradient(to right, var(--color-primary), var(--color-secondary), var(--color-accent));
    --toastify-color-progress-dark: var(--color-primary);
    --toastify-color-progress-info: var(--color-info);
    --toastify-color-progress-success: var(--color-success);
    --toastify-color-progress-warning: var(--color-warning);
    --toastify-color-progress-error: var(--color-error);
    /* used to control the opacity of the progress trail */
    --toastify-color-progress-bgo: 0.2;
}

/* DaisyUI Toast 自定义样式 - 使用 CSS 变量控制字体大小 */
.Toastify__toast-container {
    width: auto;
    max-width: var(--toastify-toast-width);
    padding: 0.5rem;
    font-family: var(--toastify-font-family);
}

.Toastify__toast {
    font-size: var(--toastify-font-size) !important;
    line-height: var(--toastify-line-height) !important;
    font-weight: var(--toastify-font-weight) !important;
    padding: var(--toastify-toast-padding) !important;
    margin-bottom: 0.375rem !important;
    min-height: var(--toastify-toast-min-height) !important;
    border-radius: var(--toastify-toast-bd-radius) !important;
    box-shadow: var(--toastify-toast-shadow) !important;
    font-family: var(--toastify-font-family) !important;
    color: var(--color-base-content) !important;
}

.Toastify__toast-body {
    padding: 0 !important;
    margin: 0 !important;
    font-size: var(--toastify-font-size);
    line-height: var(--toastify-line-height);
    font-weight: var(--toastify-font-weight);
    color: var(--color-base-content) !important;
}

.Toastify__toast-icon {
    width: calc(var(--toastify-font-size) * 1.5) !important;
    height: calc(var(--toastify-font-size) * 1.5) !important;
    margin-inline-end: 0.5rem !important;
    flex-shrink: 0;
}

.Toastify__close-button {
    width: calc(var(--toastify-font-size) * 1.25) !important;
    height: calc(var(--toastify-font-size) * 1.25) !important;
    font-size: calc(var(--toastify-font-size) * 0.9) !important;
    opacity: 0.6;
    transition: opacity 0.2s ease;
}

.Toastify__close-button:hover {
    opacity: 1;
}

.Toastify__progress-bar {
    height: 2px !important;
    border-radius: var(--radius-field, 0.25rem);
}

/* 响应式调整 - 移动端字体更小 */
@media (max-width: 640px) {
    :root {
        --toastify-font-size: 0.7rem;
        --toastify-line-height: 1.25;
        --toastify-toast-width: calc(100vw - 1rem);
        --toastify-toast-padding: 0.5rem;
        --toastify-toast-min-height: 2.5rem;
    }
    
    .Toastify__toast-container {
        width: calc(100vw - 1rem);
        max-width: none;
        padding: 0 0.5rem;
        left: 50% !important;
        right: auto !important;
        transform: translateX(-50%);
    }
    
    .Toastify__toast {
        margin-bottom: 0.25rem !important;
    }
}

/* Toast 类型样式 */
.toast-success-daisyui,
.toast-error-daisyui,
.toast-warning-daisyui,
.toast-info-daisyui,
.toast-default-daisyui {
    font-size: var(--toastify-font-size) !important;
    line-height: var(--toastify-line-height) !important;
    font-weight: var(--toastify-font-weight) !important;
    color: var(--color-base-content) !important;
}

/* 确保所有内容都使用正常的黑色文字 */
.Toastify__toast-container .Toastify__toast,
.Toastify__toast-container .Toastify__toast-body,
.Toastify__toast-container .Toastify__toast-body > div {
    color: var(--color-base-content) !important;
}


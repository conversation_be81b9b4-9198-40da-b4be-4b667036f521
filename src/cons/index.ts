export enum ROLES {
    FREE = 'FREE',
    PAID = 'PAID',
    LIMITED = 'LIMITED'
}


export enum ASSET_SOURCE {
    MANUAL = 'MANUAL',
    COLLECTION = 'COLLECTION'
}

export enum HANDLE_STATUS {
    NONE = 'NONE',
    PROCESSING = 'PROCESSING',
    PREPROCESSING = 'PREPROCESSING',
    PENDING = 'PENDING',
    DOING = 'DOING',
    SUCCESS = 'SUCCESS',
    FAILED = 'FAILED',
}

export enum ASSET_PROPERTIY {
    ASR_TEXT = 'asr_text',
    ANALYSIS_RESULT = 'analysis_results',
    VIDEO_CONTENT = 'video_content',
    VIDEO_SUMMARY = 'video_summary'
}

export enum APPROVAL_STATUS {
    PENDING = 'PENDING',
    APPROVED = 'APPROVED',
    REJECTED = 'REJECTED'
}

export enum ASSET_EINO_NODE_NAMES {
    //  检测视频信息
    CHECK_EXTRACT_INFO = 'CHECK_EXTRACT_INFO',
    //  上传视频
    UPLOAD_VIDEO = 'UPLOAD_VIDEO',
    //  上传视频回调
    UPLOAD_VIDEO_CALLBACK = 'UPLOAD_VIDEO_CALLBACK',
    //  解析视频
    VIDEO_PARSE = 'VIDEO_PARSE',
    //  生成视频雪碧图
    VIDEO_SNAPSHOTS = 'VIDEO_SNAPSHOTS',
    //  视频处理（包含解析视频、生成视频雪碧图）
    SUB_VIDEO_PROCESS = 'SUB_VIDEO_PROCESS',
    //  视频处理回调
    VIDEO_PROCESS_CALLBACK = 'VIDEO_PROCESS_CALLBACK',
    // 复用提取数据
    PROCESS_REUSE_EXTRACT_DATA = 'PROCESS_REUSE_EXTRACT_DATA',
    //  扣子解析
    COZE_ANALYSIS = 'COZE_ANALYSIS',
    //  扣子解析回调
    COZE_ANALYSIS_CALLBACK = 'COZE_ANALYSIS_CALLBACK',
    //  扣子生成选题
    COZE_GENERATION = 'COZE_GENERATION',
    //  扣子生成选题回调
    COZE_GENERATION_CALLBACK = 'COZE_GENERATION_CALLBACK',
}
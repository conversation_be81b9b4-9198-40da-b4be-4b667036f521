# 项目概述

## 项目名称
qihaozhushou-app - 七号助手应用

## 技术栈
- **前端框架**: React 18.2.0 + TypeScript
- **构建工具**: Vite 5.1.4
- **样式**: TailwindCSS 4.1.4 + DaisyUI 5.0.22
- **状态管理**: Zustand 5.0.3
- **HTTP客户端**: Axios 1.8.4 + openapi-fetch 0.14.0
- **表单**: Formily 2.3.3
- **路由**: React Router Dom 7.5.0
- **通知**: React Toastify 11.0.5

## 项目结构
- `src/api/` - API客户端和生成的类型
- `src/components/` - 组件库
- `src/pages/` - 页面组件
- `src/models/` - 状态管理 (Zustand)
- `src/utils/` - 工具函数
- `src/hooks/` - 自定义hooks

## 核心功能
这是一个专注于内容创作和数据分析的工具，包含：
- 抖音收藏夹同步
- 关键词监控
- 账户监控
- 素材管理
- 主题分析
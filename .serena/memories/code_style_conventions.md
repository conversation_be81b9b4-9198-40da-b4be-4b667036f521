# 代码风格和约定

## TypeScript 风格
- 使用严格类型检查
- API 类型通过 openapi-typescript 自动生成
- 组件使用 React.FC 类型

## 组件风格
- 优先使用函数组件和 hooks
- 使用 DaisyUI 组件库
- TailwindCSS 用于样式
- 使用 clsx 进行条件样式

## 状态管理
- 使用 Zustand 进行状态管理
- 每个功能模块有独立的 store (auth.ts, assets.ts 等)

## API 调用
- 使用生成的 apiClient 进行API调用
- 有传统的 axios 实例 (api) 和新的 openapi-fetch 客户端 (apiClient)
- 错误处理使用 react-toastify

## 组件模式
- Modal 使用 ref 控制显示/隐藏
- 表格使用 DaisyUI table 组件
- 分页有自定义实现
- Loading 状态统一处理
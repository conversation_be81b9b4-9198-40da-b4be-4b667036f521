---
description: 
globs: 
alwaysApply: false
---
# Tailwind (React)

# Best Practices for Mobile-First Web Apps with Tai<PERSON><PERSON> and React

## Mobile-First Design
- **Design for mobile screens first**, then scale up to larger screens
- Use Tailwind's responsive prefixes (`sm:`, `md:`, `lg:`, `xl:`) to adjust layouts

## Consistent Design System
- **Create a design system** with consistent colors, typography, spacing
- Utilize `tailwind.config.js` to define custom design tokens

## Performance Optimization
- Use **React.lazy()** and **Suspense** for code-splitting
- Implement **virtualization** for long lists using `react-window`

## Responsive Typography
- Use Tailwind's text utilities with responsive prefixes
- Consider **fluid typography** for seamless scaling

## Accessibility
- Ensure proper **color contrast ratios**
- Use **semantic HTML** and **ARIA attributes**
- Implement **keyboard navigation**

## Touch-Friendly UI
- Make interactive elements at least **44x44 pixels**
- Implement **touch gestures** where appropriate

## Error Handling
- Implement **error boundaries** in React
- Provide clear feedback for user actions

## Animation
- Use **subtle animations** to enhance UX
- Utilize Tailwind's transitions or **Framer Motion**

## Form Handling
- Use **Formik** or **react-hook-form**
- Implement proper **form validation**

## Code Organization
- Follow a **consistent folder structure**
- Use **custom hooks** to reuse logic

## Native-like Features
- Implement **pull-to-refresh**
- Use **smooth scrolling**
- Consider **react-spring** for animations

## Medication Tracking Feature
### UI Components:
- Time-based pill displays (Morning/Afternoon/Night)
- Action buttons (Take/Skip)
- Confirmation modals

### Data Flow:
- Log user interactions (action, timestamp, notes)
- Store medication records

### Reporting:
- Calculate adherence percentage
- Identify medication trends
- Generate usage statistics

### Dashboard:
- Display monthly reports
- Show adherence metrics
- Provide actionable insights
#!/usr/bin/env node

/**
 * 自动生成 API 类型定义脚本
 * 
 * 使用方法:
 * node scripts/generate-api-types.js
 * 
 * 或者添加到 package.json scripts:
 * "generate-api": "node scripts/generate-api-types.js"
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

// 配置
const CONFIG = {
  // OpenAPI 规范 URL
  openApiUrl: 'http://localhost:8200/common/docs/openapi/getOpenAPISpec',
  // 本地 OpenAPI 规范文件路径
  specFilePath: 'openapi-spec.json',
  // 生成的类型文件路径
  typesFilePath: 'src/api/generated/types.ts',
  // 备份目录
  backupDir: 'backups',
}

/**
 * 日志输出
 */
function log(message, type = 'info') {
  const timestamp = new Date().toISOString()
  const prefix = {
    info: '📝',
    success: '✅',
    error: '❌',
    warning: '⚠️',
  }[type] || '📝'
  
  console.log(`${prefix} [${timestamp}] ${message}`)
}

/**
 * 执行命令
 */
function execCommand(command, description) {
  try {
    log(`执行: ${description}`)
    const result = execSync(command, { encoding: 'utf8', stdio: 'pipe' })
    log(`完成: ${description}`, 'success')
    return result
  } catch (error) {
    log(`失败: ${description} - ${error.message}`, 'error')
    throw error
  }
}

/**
 * 检查文件是否存在
 */
function fileExists(filePath) {
  return fs.existsSync(filePath)
}

/**
 * 创建目录
 */
function ensureDir(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true })
    log(`创建目录: ${dirPath}`)
  }
}

/**
 * 备份现有文件
 */
function backupFile(filePath) {
  if (!fileExists(filePath)) return

  ensureDir(CONFIG.backupDir)
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
  const fileName = path.basename(filePath)
  const backupPath = path.join(CONFIG.backupDir, `${fileName}.${timestamp}.backup`)
  
  fs.copyFileSync(filePath, backupPath)
  log(`备份文件: ${filePath} -> ${backupPath}`)
}

/**
 * 下载 OpenAPI 规范
 */
async function downloadOpenApiSpec() {
  try {
    log('开始下载 OpenAPI 规范...')
    
    // 使用 curl 下载
    const curlCommand = `curl -s "${CONFIG.openApiUrl}"`
    const specContent = execCommand(curlCommand, '下载 OpenAPI 规范')
    
    // 验证 JSON 格式
    try {
      JSON.parse(specContent)
    } catch (error) {
      throw new Error('下载的内容不是有效的 JSON 格式')
    }
    
    // 备份现有文件
    backupFile(CONFIG.specFilePath)
    
    // 保存到文件
    fs.writeFileSync(CONFIG.specFilePath, specContent, 'utf8')
    log(`OpenAPI 规范已保存到: ${CONFIG.specFilePath}`, 'success')
    
  } catch (error) {
    log(`下载 OpenAPI 规范失败: ${error.message}`, 'error')
    throw error
  }
}

/**
 * 生成 TypeScript 类型
 */
function generateTypes() {
  try {
    log('开始生成 TypeScript 类型...')
    
    // 确保输出目录存在
    const outputDir = path.dirname(CONFIG.typesFilePath)
    ensureDir(outputDir)
    
    // 备份现有类型文件
    backupFile(CONFIG.typesFilePath)
    
    // 生成类型
    const generateCommand = `npx openapi-typescript ${CONFIG.specFilePath} -o ${CONFIG.typesFilePath}`
    execCommand(generateCommand, '生成 TypeScript 类型')
    
    log(`TypeScript 类型已生成到: ${CONFIG.typesFilePath}`, 'success')
    
  } catch (error) {
    log(`生成 TypeScript 类型失败: ${error.message}`, 'error')
    throw error
  }
}

/**
 * 验证生成的文件
 */
function validateGeneratedFiles() {
  const filesToCheck = [
    CONFIG.specFilePath,
    CONFIG.typesFilePath,
  ]
  
  for (const filePath of filesToCheck) {
    if (!fileExists(filePath)) {
      throw new Error(`文件不存在: ${filePath}`)
    }
    
    const stats = fs.statSync(filePath)
    if (stats.size === 0) {
      throw new Error(`文件为空: ${filePath}`)
    }
  }
  
  log('所有生成的文件验证通过', 'success')
}

/**
 * 清理临时文件
 */
function cleanup() {
  // 可以在这里添加清理逻辑
  log('清理完成')
}

/**
 * 主函数
 */
async function main() {
  try {
    log('开始生成 API 类型定义...')
    
    // 检查依赖
    try {
      execSync('npx openapi-typescript --version', { stdio: 'pipe' })
    } catch (error) {
      throw new Error('openapi-typescript 未安装，请运行: pnpm add -D openapi-typescript')
    }
    
    // 下载 OpenAPI 规范
    await downloadOpenApiSpec()
    
    // 生成 TypeScript 类型
    generateTypes()
    
    // 验证生成的文件
    validateGeneratedFiles()
    
    // 清理
    cleanup()
    
    log('🎉 API 类型定义生成完成！', 'success')
    log('📁 生成的文件:')
    log(`   - OpenAPI 规范: ${CONFIG.specFilePath}`)
    log(`   - TypeScript 类型: ${CONFIG.typesFilePath}`)
    log('📖 使用说明请查看: src/api/generated/README.md')
    
  } catch (error) {
    log(`生成失败: ${error.message}`, 'error')
    process.exit(1)
  }
}

// 运行主函数
if (require.main === module) {
  main()
}

module.exports = {
  main,
  downloadOpenApiSpec,
  generateTypes,
  validateGeneratedFiles,
}
